const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  deleteByUserId,
};

async function getAll() {
  return await db.ThemeBannerImage.findAll({
    order: [["id", "DESC"]],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // validate

  const record = await db.ThemeBannerImage.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to ThemeBannerImage and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.ThemeBannerImage.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
async function deleteByUserId(userId) {
  const records = await db.ThemeBannerImage.findAll({
    where: {
      user_id: userId,
    },
  });

  if (records.length !== 0) {
    await Promise.all(records.map((record) => record.destroy()));
  }
}
