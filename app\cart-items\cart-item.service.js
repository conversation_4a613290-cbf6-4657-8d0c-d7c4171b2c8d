﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
};

db.CartItem.belongsTo(db.Product, {
  as: "productInfo",
  through: "products",
  foreignKey: "product_id",
  otherKey: "product_id",
});

async function getAll(params) {
  const { cartId } = params;
  const where = {};
  if (cartId) {
    where.cart_id = cartId;
  }
  return await db.CartItem.findAll({
    order: [["id", "DESC"]],
    where: where,
    include: [
      {
        model: db.Product,
        as: "productInfo",
        attributes: ["id", "name"],
      },
    ],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const { cart_id, product_id, quantity } = params;

  let existingItem = await db.CartItem.findOne({
    where: { cart_id, product_id },
  });

  if (existingItem) {
    existingItem.quantity += quantity;
    await existingItem.save();
    return existingItem;
  } else {
    return await db.CartItem.create(params);
  }
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.CartItem.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
