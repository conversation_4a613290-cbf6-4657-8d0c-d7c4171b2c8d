﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const ngoTypeService = require("./ngo-type.service");
const { logAction } = require("../_helpers/logger");

// routes
router.get("/", getAll);
router.post("/", createSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  ngoTypeService
    .create(req.body)
    .then(() =>
      res.json({ status: true, message: "Record created successfully" })
    )
    .catch(next);
}

function getAll(req, res, next) {
  const { pageName } = req.query;
  ngoTypeService
    .getAll(req.query, req?.headers?.isadmin)
    .then((records) => {
      logAction(5, `Fetched all ngo types in ${pageName}`, pageName);
      return res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  ngoTypeService
    .getById(req.params.id)
    .then((user) => res.json(user))
    .catch(next);
}

function createSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    description: Joi.string().allow(null, ""),
    status: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    description: Joi.string().allow(null, ""),
    status: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  ngoTypeService
    .update(req.params.id, req.body)
    .then((user) => res.json(user))
    .catch(next);
}

function _delete(req, res, next) {
  ngoTypeService
    .delete(req.params.id)
    .then(() =>
      res.json({ status: true, message: "Record deleted successfully" })
    )
    .catch(next);
}
