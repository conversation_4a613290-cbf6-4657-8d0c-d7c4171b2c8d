const express = require("express");
const axios = require("axios");
const fs = require("fs");
const router = express.Router();
const { getAccessToken } = require("../_helpers/googleAuth");

const multer = require("multer");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/google-photos";
    cb(null, __basedir + "uploads/google-photos");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

const uploadConfig = multer({
  storage: storage,
});

// const accessToken = `******************************************************************************************************************************************************************************************************************************`;

router.post("/upload", uploadConfig.single("file"), uploadVideo);

// Endpoint: POST /api/google-photos/upload
async function uploadVideo(req, res) {
  console.log("coming");
  const accessToken = await getAccessToken();

  // const accessToken = `********************************************************************************************************************************************************************************************************************************`;

  console.log("-------------");
  console.log(accessToken);
  console.log("-------------");

  try {
    const file = req.file;
    const { darpanId } = req.body;

    const ngoRecord = await db.Ngo.findOne({
      where: { darpan_id: darpanId },
    });

    if (!ngoRecord) {
      return res.status(404).json({
        success: false,
        message: "NGO not found for given darpanId",
      });
    }

    const kycRecord = await db.KycInformation.findOne({
      where: { ngo_id: ngoRecord.id },
    });

    if (!kycRecord) {
      return res.status(404).json({
        success: false,
        message: "KYC record not found for given darpanId",
      });
    }

    if (!file) {
      return res
        .status(400)
        .json({ success: false, message: "No file uploaded" });
    }

    const filePath = file.path;
    const fileName = file.originalname;

    const fileData = fs.readFileSync(filePath);

    console.log("File data:", fileData);

    // Step 1: Upload the file to Google Photos to get uploadToken
    const uploadRes = await axios.post(
      "https://photoslibrary.googleapis.com/v1/uploads",
      fileData,
      {
        headers: {
          "Content-type": "application/octet-stream",
          "X-Goog-Upload-File-Name": fileName,
          "X-Goog-Upload-Protocol": "raw",
          Authorization: `Bearer ${accessToken}`,
        },
        maxBodyLength: Infinity,
      }
    );

    console.log("Upload response:", uploadRes.data);

    const uploadToken = uploadRes.data;

    // Step 2: Create Album
    const albumData = await createAlbum(darpanId, accessToken);
    const albumId = albumData.id;

    // Step 3: Upload to album
    const mediaItemResponse = await uploadToAlbum(
      uploadToken,
      albumId,
      fileName,
      accessToken
    );

    kycRecord.uploadToken = uploadToken;
    kycRecord.albumId = albumId;
    kycRecord.status = "In Review";
    kycRecord.uploadResponse = JSON.stringify(mediaItemResponse); // store as text

    await kycRecord.save();

    return res.json({
      success: true,
      albumId,
      mediaItem: mediaItemResponse,
    });
  } catch (error) {
    console.error(
      "Google Photos upload error:",
      error.response?.data || error.message
    );
    return res.status(500).json({
      success: false,
      message: "Upload failed",
      details: error.response?.data || error.message,
    });
  }
}

async function createAlbum(title, accessToken) {
  const data = {
    album: {
      title: title, // Using darpanId or any title passed
    },
  };

  try {
    const response = await axios.post(
      "https://photoslibrary.googleapis.com/v1/albums",
      data,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );
    return response.data; // returns created album info
  } catch (error) {
    console.error(
      "Album creation failed:",
      error.response?.data || error.message
    );
    throw error;
  }
}

async function uploadToAlbum(
  uploadToken,
  albumId,
  description = "Uploaded via API",
  accessToken
) {
  const data = {
    albumId,
    newMediaItems: [
      {
        description,
        simpleMediaItem: {
          uploadToken,
        },
      },
    ],
  };

  const response = await axios.post(
    "https://photoslibrary.googleapis.com/v1/mediaItems:batchCreate",
    data,
    {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
    }
  );

  return response.data;
}

module.exports = router;
