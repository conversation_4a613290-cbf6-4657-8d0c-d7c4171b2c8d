const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const ngoSettingService = require("./ngo-setting.service");
const { logAction } = require("../_helpers/logger");

// routes
router.get("/:ngoId", getAll);
router.post("/:ngoId", create);
// router.get("/:id", getById);
router.put("/:id", update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  const ngoId = req.params.ngoId;
  ngoSettingService
    .create(ngoId, req.body)
    .then(() =>
      res.json({ status: true, message: "Record created successfully" })
    )
    .catch(next);
}

function getAll(req, res, next) {
  const ngoId = req.params.ngoId;

  ngoSettingService
    .getAll(ngoId)
    .then((records) => {
      logAction(
        5,
        `Fetched all setting of NGO with ID ${ngoId}`,
        req.query.pageName
      );
      return res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  ngoSettingService
    .getById(req.params.id)
    .then((portalUserCategory) => res.json(portalUserCategory))
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    master_setting_id: Joi.number().required(),
    ngo_id: Joi.number().required(),
    value: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  ngoSettingService
    .update(req.params.id, req.body)
    .then((portalUserCategory) => res.json(portalUserCategory))
    .catch(next);
}

function _delete(req, res, next) {
  ngoSettingService
    .deleteByNGOId(req.params.id)
    .then(() =>
      res.json({ status: true, message: "Records deleted successfully" })
    )
    .catch(next);
}
