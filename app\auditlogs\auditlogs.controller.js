const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const auditlogService = require("./auditlog.service");

// routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  auditlogService
    .create(req.body)
    .then(() =>
      res.json({ status: true, message: "Record created successfully" })
    )
    .catch(next);
}

function getAll(req, res, next) {
  auditlogService
    .getAll(req.query)
    .then((records) => {
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  auditlogService
    .getById(req.params.id)
    .then((auditlog) => res.json(auditlog))
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    userId: Joi.string().required(),
    description: Joi.string().required(),
    pageName: Joi.string().allow(null, ''),
    type: Joi.string().allow(null, ''),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  auditlogService
    .update(req.params.id, req.body)
    .then((auditlog) => res.json(auditlog))
    .catch(next);
}

function _delete(req, res, next) {
  auditlogService
    .delete(req.params.id)
    .then(() =>
      res.json({ status: true, message: "Record deleted successfully" })
    )
    .catch(next);
}
