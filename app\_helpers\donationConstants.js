const DR_TIP_PERCENT = 0.08; // 8%
const USER_CONVENIENCE_PERCENT = 0.05; // 5%
const GATEWAY_CHARGES_PERCENT = 0.0118; // 1.18%
const NGO_PLATFORM_PERCENT = 0.08; // 8%

/**
 * Calculate order-related donation amounts
 * @param {number} baseAmount - The base donation amount
 * @returns {object} Object containing all calculated amounts for order
 */
function calculateOrderAmounts(baseAmount) {
  const drTip = baseAmount * DR_TIP_PERCENT;
  const userConvenience = baseAmount * USER_CONVENIENCE_PERCENT;
  const totalAmount = baseAmount + drTip + userConvenience;

  return {
    baseAmount: parseFloat(baseAmount.toFixed(2)),
    drTip: parseFloat(drTip.toFixed(2)),
    userConvenience: parseFloat(userConvenience.toFixed(2)),
    totalAmount: parseFloat(totalAmount.toFixed(2)),
  };
}

/**
 * Calculate transaction-related donation amounts after payment gateway charges
 * @param {number} totalAmount - Total amount received (from order)
 * @param {number} baseAmount - Base donation amount
 * @param {number} orderDrTip - DR tip from order calculation
 * @param {number} orderUserConvenience - User convenience from order calculation
 * @returns {object} Object containing all calculated amounts for transaction
 */
function calculateTransactionAmounts(
  totalAmount,
  baseAmount,
  orderDrTip,
  orderUserConvenience
) {
  // Payment Gateway Charges: 1.18% of total amount
  const paymentGatewayCharges = totalAmount * GATEWAY_CHARGES_PERCENT;

  // Monies Available for Routing
  const moniesAvailableForRouting = totalAmount - paymentGatewayCharges;

  // DR Tip after gateway charges: orderDrTip - (orderDrTip * 1.18%)
  const drTipAfterCharges = orderDrTip - orderDrTip * GATEWAY_CHARGES_PERCENT;

  // User Convenience after gateway charges: orderUserConvenience - (orderUserConvenience * 1.18%)
  const userConvenienceAfterCharges =
    orderUserConvenience - orderUserConvenience * GATEWAY_CHARGES_PERCENT;

  // NGO Platform Convenience Fee: (baseAmount * 8%) - (baseAmount * 1.18%)
  const ngoPlatformConvenienceFee =
    baseAmount * NGO_PLATFORM_PERCENT - baseAmount * GATEWAY_CHARGES_PERCENT;

  // NGO Net Donation: baseAmount - (baseAmount * 8%)
  const ngoNetDonation = baseAmount - baseAmount * NGO_PLATFORM_PERCENT;

  return {
    paymentGatewayCharges: parseFloat(paymentGatewayCharges.toFixed(2)),
    moniesAvailableForRouting: parseFloat(moniesAvailableForRouting.toFixed(2)),
    drTipAfterCharges: parseFloat(drTipAfterCharges.toFixed(2)),
    userConvenienceAfterCharges: parseFloat(
      userConvenienceAfterCharges.toFixed(2)
    ),
    ngoPlatformConvenienceFee: parseFloat(ngoPlatformConvenienceFee.toFixed(2)),
    ngoNetDonation: parseFloat(ngoNetDonation.toFixed(2)),
  };
}

module.exports = {
  DR_TIP_PERCENT,
  USER_CONVENIENCE_PERCENT,
  GATEWAY_CHARGES_PERCENT,
  NGO_PLATFORM_PERCENT,
  calculateOrderAmounts,
  calculateTransactionAmounts,
};
