﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const slideService = require("./slide.service");
const multer = require("multer");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/slide-images";
    cb(null, __basedir + "uploads/slide-images/");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

// STAND ALONE CONFIG
const uploadConfig = multer({
  storage: storage,
});

// routes
router.get("/", getAll);
router.post("/", uploadConfig.single("file"), updateSchema, create);
router.get("/:id", getById);
router.put("/:id", uploadConfig.single("file"), updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  slideService
    .create(req.body)
    .then(() =>
      res.json({ status: true, message: "Record created successfully" })
    )
    .catch(next);
}

function getAll(req, res, next) {
  slideService
    .getAll(req.query, req?.headers?.isadmin)
    .then((records) => {
      return res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  slideService
    .getById(req.params.id)
    .then((user) => res.json(user))
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    title: Joi.string().required(),
    imageName: Joi.string().required(),
    text: Joi.string().required(),
    status: Joi.string().required(),
    buttonText: Joi.string().optional().allow(null, ""),
    showActionButton: Joi.string().required(),
    link: Joi.string().optional().allow(null, ""),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  slideService
    .update(req.params.id, req.body)
    .then((user) => res.json(user))
    .catch(next);
}

function _delete(req, res, next) {
  slideService
    .delete(req.params.id)
    .then(() =>
      res.json({ status: true, message: "Record deleted successfully" })
    )
    .catch(next);
}
