﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const campaignMilestoneService = require("./campaign-milestone.service");
const multer = require("multer");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/campaigns-milestones";
    cb(null, __basedir + "uploads/campaigns-milestones/");
  },
  filename: (req, file, cb) => {
    // const fileName = file.originalname.toLowerCase().split(" ").join("-");
    cb(null, file.originalname);
  },
});

const uploadConfig = multer({
  storage: storage,
  // fileFilter: (req, file, cb) => {
  // 	if (
  // 		file.mimetype == "application/pdf"
  // 	) {
  // 		cb(null, true);
  // 	} else {
  // 		cb(null, false);
  // 		return cb(new Error("Only .pdf format allowed!"));
  // 	}
  // },
});
// routes
router.get("/", getAll);
router.get("/getCampaignMilestones", getCampaignMileStones);
router.post("/", uploadConfig.single("file"), updateSchema, create);
// router.get("/:id", getById);
router.put("/:id", uploadConfig.single("file"), updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  campaignMilestoneService
    .create(req.body)
    .then(() => {
      logRequest(
        req,
        `Added milestone: ${req.body.description} for campaign ID ${req.body.campaign_id}, NGO ID ${req.body.ngo_id}, Collection Date: ${req.body.collection_date}`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  campaignMilestoneService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all campaign milestones", "READ");
      res.json(records);
    })
    .catch(next);
}

function getCampaignMileStones(req, res, next) {
  const campaignId = req.query.campaignId;
  if (!campaignId) {
    return res
      .status(400)
      .json({ status: false, message: "campaignId is required for search" });
  }

  campaignMilestoneService
    .getCampaignMileStones(campaignId)
    .then((campaigns) => {
      logRequest(
        req,
        `Fetched milestones for campaign ID ${campaignId}`,
        "READ"
      );
      res.json(campaigns);
    })
    .catch(next);
}
function getById(req, res, next) {
  campaignMilestoneService
    .getById(req.params.id)
    .then((user) => res.json(user))
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    description: Joi.string().required(),
    fileName: Joi.string().required(),
    collection_date: Joi.date().required(),
    portal_user_id: Joi.number().integer().required(),
    ngo_id: Joi.number().optional().allow(null),
    campaign_id: Joi.number().integer().required(),
  });

  validateRequest(req, next, schema);
}

function update(req, res, next) {
  campaignMilestoneService
    .update(req.params.id, req.body)
    .then((milestone) => {
      logRequest(
        req,
        `Updated milestone ${milestone?.id}: ${milestone?.description} for campaign ID ${milestone?.campaign_id}, NGO ID ${milestone?.ngo_id}, Collection Date: ${milestone?.collection_date}`,
        "UPDATE"
      );
      res.json(milestone);
    })
    .catch(next);
}

function _delete(req, res, next) {
  campaignMilestoneService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted milestone with ID ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
