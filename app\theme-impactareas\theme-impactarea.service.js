const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  deleteByUserId,
};

async function getAll(params) {
  const where = {};
  const { ngoId } = params;
  if (ngoId) {
    where.ngo_id = ngoId;
  }
  return await db.ThemeImpactArea.findAll({
    order: [["id", "DESC"]],
    where: where,
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // validate

  const record = await db.ThemeImpactArea.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to ThemeImpactArea and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.ThemeImpactArea.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
async function deleteByUserId(userId) {
  const records = await db.ThemeImpactArea.findAll({
    where: {
      user_id: userId,
    },
  });

  if (records.length !== 0) {
    await Promise.all(records.map((record) => record.destroy()));
  }
}
