﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  getCampaignMileStones,
};

async function getAll() {
  return await db.CampaignMilestone.findAll({
    order: [["id", "DESC"]],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const record = await db.CampaignMilestone.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.CampaignMilestone.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}

async function getCampaignMileStones(campaignId) {
  let where = {};
  if (campaignId) {
    where.campaign_id = campaignId;
  }
  const result = await db.CampaignMilestone.findAll({
    where: where,
  });
  if (!result) return "result not found for this status";
  return result;
}
