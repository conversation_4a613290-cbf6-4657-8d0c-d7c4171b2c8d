{"name": "doright", "version": "1.0.0", "main": "index.js", "scripts": {"start": "nodemon --ignore token.json ./app.js", "start:dev": "nodemon ./app.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.7.9", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "cors": "^2.8.5", "crypto": "^1.0.1", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "draft-js": "^0.11.7", "draft-js-export-html": "^1.4.1", "express": "^4.21.1", "firebase-admin": "^13.4.0", "googleapis": "^150.0.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.11.3", "nodemailer": "^6.9.16", "razorpay": "^2.9.5", "rootpath": "^0.1.2", "sequelize": "^6.37.5", "sequelize-cli": "^6.6.2"}, "devDependencies": {"nodemon": "^3.1.7"}}