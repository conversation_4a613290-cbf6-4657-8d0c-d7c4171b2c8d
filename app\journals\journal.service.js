﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getAllBySenderId,
  getById,
  create,
  update,
  delete: _delete,
  addAssignAndReassignJournals,
};

db.Journal.belongsTo(db.Ngo, {
  as: "ngoInfo",
  through: "ngos",
  foreignKey: "ngo_id",
  otherKey: "ngo_id",
});

async function getAll(ngoId) {
  let where = {};
  if (ngoId) {
    where.ngo_id = ngoId;
  }
  const result = await db.Journal.findAll({
    where: where,
    order: [["createdAt", "DESC"]],
  });
  if (!result) return "result not found for this status";
  return result;
}

async function getAllBySenderId(senderId) {
  let where = {};
  if (senderId) {
    where.sender_id = senderId;
  }
  const result = await db.Journal.findAll({
    where: where,
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name"],
      },
    ],
  });
  if (!result) return "result not found for this sender";
  return result;
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const record = await db.Journal.create(params);
  return record;
}
// async function addAssignAndReassignJournals(body, senderId, assigneeId) {
//   const record = await db.Journal.create(params);
//   return record;
// }

async function addAssignAndReassignJournals(
  selectedNgoIds,
  senderId,
  assigneeId
) {
  const assigneeRecord = await db.PortalUser.findByPk(assigneeId);

  const records = await Promise.all(
    selectedNgoIds.map(async (ngoId) => {
      const ngoRecord = await db.Ngo.findByPk(ngoId);

      // Check if `assigned_on` exists
      const isReassigned = !!ngoRecord?.assigned_on;

      return {
        sender_id: senderId,
        ngo_id: ngoId,
        description: isReassigned
          ? `Reassigned NGO ${ngoRecord?.name || ngoId} to admin ${
              assigneeRecord?.fullname || "Unknown"
            }`
          : `Assigned NGO ${ngoRecord?.name || ngoId} to admin ${
              assigneeRecord?.fullname || "Unknown"
            }`,
      };
    })
  );

  const result = await db.Journal.bulkCreate(records);
  return result;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.Journal.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
