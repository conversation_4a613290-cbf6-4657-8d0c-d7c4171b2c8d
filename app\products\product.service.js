﻿const { Sequelize, where, Op } = require("sequelize");
const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  patch,
  delete: _delete,
  getByNgoId,
  deleteByNgoId,
};

db.Product.belongsTo(db.Ngo, {
  as: "ngoInfo",
  through: "ngos",
  foreignKey: "ngo_id",
  otherKey: "ngo_id",
});

db.Product.belongsTo(db.Collection, {
  as: "collectionInfo",
  through: "collections",
  foreignKey: "collection_id",
  otherKey: "collection_id",
});

async function getAll(ngoId) {
  const where = {};
  if (ngoId) where.ngo_id = ngoId;
  return await db.Product.findAll({
    order: [["updatedAt", "DESC"]],
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name"],
      },
      {
        model: db.Collection,
        as: "collectionInfo",
        attributes: ["id", "name"],
      },
    ],
    where,
  });
}
async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  if ("name" in params && params.name) {
    await utils.validateTextForAbuse(params.name);
  }

  if ("description" in params && params.description) {
    await utils.validateTextForAbuse(params.description);
  }
  const record = await db.Product.create(params);
  return record;
}

async function update(id, params) {
  if ("name" in params && params.name) {
    await utils.validateTextForAbuse(params.name);
  }

  if ("description" in params && params.description) {
    await utils.validateTextForAbuse(params.description);
  }
  const record = await getSingleRecord(id);

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}
async function patch(id, params) {
  if ("name" in params && params.name) {
    await utils.validateTextForAbuse(params.name);
  }

  if ("description" in params && params.description) {
    await utils.validateTextForAbuse(params.description);
  }
  const record = await getSingleRecord(id);

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}
async function deleteByNgoId(id) {
  const records = await getAll(id);
  if (!records || records.length === 0) {
    return;
  }
  for (const record of records) {
    await record.destroy();
  }
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.Product.findByPk(id, {
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name"],
      },
      {
        model: db.Collection,
        as: "collectionInfo",
        attributes: ["id", "name"],
      },
    ],
  });
  if (!record) throw "Record not found";
  return record;
}

async function getByNgoId(params, page = 1, limit = 10) {
  const { ngo_id, search } = params;
  let where = {};

  if (ngo_id) {
    where.ngo_id = ngo_id;
  }

  if (search) {
    // Case-insensitive search by product name
    where.name = {
      [Op.like]: `%${search}%`,
    };
  }

  const offset = (parseInt(page) - 1) * parseInt(limit);

  const { count, rows } = await db.Product.findAndCountAll({
    where: where,
    limit: parseInt(limit),
    offset: offset,
    order: [["id", "DESC"]],
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name"],
      },
      {
        model: db.Collection,
        as: "collectionInfo",
        attributes: ["id", "name"],
      },
    ],
  });

  return {
    products: rows,
    totalCount: count,
    totalPages: Math.ceil(count / limit),
    currentPage: page,
  };
}
