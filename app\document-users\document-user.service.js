﻿const config = require("../../config.json");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcryptjs");
const db = require("../_helpers/db");
const fs = require("fs");
const path = require("path");

module.exports = {
  create,
  getById,
  update,
  patch,
  delete: _delete,
  getAll,
};

db.DocumentUser.belongsTo(db.Document, {
  as: "documentInfo",
  through: "document_masters",
  foreignKey: "documentId",
  otherKey: "documentId",
});

async function create(params) {
  const existingRecord = await db.DocumentUser.findOne({
    where: {
      ngo_id: params.ngo_id,
      documentId: params.documentId,
    },
  });

  if (existingRecord) {
    return await update(existingRecord.id, params);
  } else {
    return await db.DocumentUser.create(params);
  }
}

async function getAll(params) {
  return await db.DocumentUser.findAll({
    where: { ngo_id: params?.ngo_id },
    include: [
      {
        model: db.Document,
        as: "documentInfo",
        attributes: ["id", "name"],
      },
    ],
    order: [["id", "DESC"]],
  });
}

async function getById(id) {
  return await getDocument(id);
}

async function update(id, params) {
  const record = await getSingleRecord(id, params.ngo_id);
  if (!record) throw "Record not found";

  if (params.fileName && record.fileName) {
    const oldFilePath = path.join(
      __basedir,
      "uploads/documents",
      path.basename(record.fileName)
    );

    if (fs.existsSync(oldFilePath)) {
      fs.unlink(oldFilePath, (err) => {
        if (err) {
          console.error(`Error deleting old file ${oldFilePath}:`, err);
        } else {
          console.log(`Successfully deleted old file: ${oldFilePath}`);
        }
      });
    }
  }
  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}
async function patch(id, params) {
  const record = await getSingleRecord(id, params.ngo_id);
  if (!record) throw "Record not found";

  if (params.fileName && record.fileName) {
    const oldFilePath = path.join(
      __basedir,
      "uploads/documents",
      path.basename(record.fileName)
    );

    if (fs.existsSync(oldFilePath)) {
      fs.unlink(oldFilePath, (err) => {
        if (err) {
          console.error(`Error deleting old file ${oldFilePath}:`, err);
        } else {
          console.log(`Successfully deleted old file: ${oldFilePath}`);
        }
      });
    }
  }

  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getDocument(id);
  if (!record) throw "Record not found";

  if (record.fileName) {
    const filePath = path.join(
      __basedir,
      "uploads/documents",
      path.basename(record.fileName)
    );
    if (fs.existsSync(filePath)) {
      fs.unlink(filePath, (err) => {
        if (err) {
          console.error(`Error deleting file ${filePath}:`, err);
        } else {
          console.log(`Successfully deleted file: ${filePath}`);
        }
      });
    }
  }

  await record.destroy();
}

async function getSingleRecord(id, ngoId) {
  const record = await db.DocumentUser.findOne({
    where: {
      documentId: id,
      ngo_id: ngoId,
    },
  });
  if (!record) throw "Invalid ID";
  return record;
}

// helper functions

async function getDocument(id) {
  const document = await db.DocumentUser.findByPk(id);
  if (!document) throw "Record not found";
  return document;
}
