﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  getByStateCode,
  create,
  update,
  delete: _delete,
};

async function getAll(params) {
  return await db.Data.findAll();
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // validate

  const record = await db.Data.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.Data.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
async function getByStateCode(stateCode) {
  const record = await db.Data.findAll({
    where: { statecode: stateCode },
  });
  if (!record) throw "Record not found";
  return record;
}
