const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const authorize = require("../_middleware/authorize");
const multer = require("multer");
const uploadFile = require("../_middleware/upload");

const documentService = require("./document-user.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/documents";
    cb(null, __basedir + "uploads/documents/");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

// STAND ALONE CONFIG
const uploadConfig = multer({
  storage: storage,
});

// routes
router.get("/", getAll);
router.get("/:id", getById);
router.put("/:id", uploadConfig.single("file"), update);
router.patch("/:id", patch);
router.post("/", uploadConfig.single("file"), create);
router.delete("/:id", _delete);

module.exports = router;

function getAll(req, res, next) {
  documentService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all user documents", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  documentService
    .getById(req.params.id)
    .then((document) => {
      logRequest(
        req,
        `Fetched document with documentId ${document?.documentId} for NGO ID ${document?.ngo_id}`,
        "READ"
      );
      res.json(document);
    })
    .catch(next);
}

function create(req, res, next) {
  documentService
    .create(req.body)
    .then((document) => {
      logRequest(
        req,
        `Created a new user document with documentId ${document?.documentId} for NGO ID ${document?.ngo_id}`,
        "CREATE"
      );
      res.json(document);
    })
    .catch(next);
}

function update(req, res, next) {
  documentService
    .update(req.params.id, req.body)
    .then((document) => {
      logRequest(
        req,
        `Updated user document with documentId ${document?.documentId} for NGO ID ${document?.ngo_id}`,
        "UPDATE"
      );
      res.json(document);
    })
    .catch(next);
}

function patch(req, res, next) {
  documentService
    .patch(req.params.id, req.body)
    .then((document) => {
      logRequest(
        req,
        `Patched user document with documentId ${document?.documentId} for NGO ID ${document?.ngo_id}`,
        "UPDATE"
      );
      res.json(document);
    })
    .catch(next);
}

function _delete(req, res, next) {
  documentService
    .delete(req.params.id)
    .then(() => {
      logRequest(
        req,
        `Deleted user document with documentId ${req.body.documentId} for NGO ID ${req.body.ngo_id}`,
        "DELETE"
      );
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
