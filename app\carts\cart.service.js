﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
};

db.Cart.belongsTo(db.User, {
  as: "userInfo",
  through: "users",
  foreignKey: "user_id",
  otherKey: "user_id",
});

async function getAll(params) {
  const { userId } = params;
  const where = {};
  if (userId) {
    where.user_id = userId;
  }

  const carts = await db.Cart.findAll({
    order: [["id", "DESC"]],
    where,
    include: [
      {
        model: db.User,
        as: "userInfo",
        attributes: ["id", "fullname", "skills", "age", "gender"],
      },
    ],
  });

  // For each cart, fetch and append cartItems manually
  const result = await Promise.all(
    carts.map(async (cart) => {
      const cartItems = await db.CartItem.findAll({
        where: { cart_id: cart.id },
        order: [["id", "DESC"]],
        include: [
          {
            model: db.Product,
            as: "productInfo",
            attributes: ["id", "name", "imageName"],
          },
        ],
      });

      // Convert to JSON so we can append new property
      const cartData = cart.toJSON();
      cartData.cartItems = cartItems;

      return cartData;
    })
  );

  return result;
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const { user_id } = params;

  // Check if a pending cart already exists for the user
  let cart = await db.Cart.findOne({
    where: {
      user_id: user_id,
      status: "pending",
    },
  });

  if (!cart) {
    // Create a new cart if not found
    cart = await db.Cart.create(params);
  }

  // Fetch cart items for this cart
  const cartItems = await db.CartItem.findAll({
    where: { cart_id: cart.id },
    order: [["id", "DESC"]],
    include: [
      {
        model: db.Product,
        as: "productInfo",
        attributes: ["id", "name"],
      },
    ],
  });

  // Append items to the cart object
  return {
    ...cart.toJSON(),
    items: cartItems,
  };
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.Cart.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
