﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const settingMasterService = require("./ngo-setting-master.service");
const { logAction } = require("../_helpers/logger");

// routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  settingMasterService
    .create(req.body)
    .then(() => {
      logAction(5, "Fetched all settings", req.query.pageName);
      return res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  settingMasterService
    .getAll(req.query)
    .then((records) => {
      // return Promise.all(records.map(async element => {
      //     element.agentList = await db.Broker.count({ where: { settingMasterId: 3, brokerId: element.id } });
      //     return element;
      // })).then(() => {
      //     res.json(records);
      //     next();
      // });

      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  settingMasterService
    .getById(req.params.id)
    .then((user) => res.json(user))
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    type: Joi.string().required(),
    default_value: Joi.string().required(),
    status: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  settingMasterService
    .update(req.params.id, req.body)
    .then((user) => res.json(user))
    .catch(next);
}

function _delete(req, res, next) {
  settingMasterService
    .delete(req.params.id)
    .then(() =>
      res.json({ status: true, message: "Record deleted successfully" })
    )
    .catch(next);
}
