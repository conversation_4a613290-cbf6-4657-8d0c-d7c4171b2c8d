const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    ngo_id: { type: DataTypes.INTEGER, allowNull: false },
    status: { type: DataTypes.STRING(45), allowNull: true },
    verification_link: { type: DataTypes.TEXT, allowNull: true },
    uniqueId: { type: DataTypes.TEXT, allowNull: true },
    uploadToken: { type: DataTypes.TEXT, allowNull: true },
    albumId: { type: DataTypes.TEXT, allowNull: true },
    uploadResponse: { type: DataTypes.TEXT, allowNull: true },
  };

  const options = {
    defaultScope: {
      // exclude sensitive data by default
    },
    scopes: {
      // include all fields with this scope
      withAll: { attributes: {} },
    },
  };

  return sequelize.define("kyc_informations", attributes, options);
}
