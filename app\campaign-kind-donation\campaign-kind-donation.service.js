﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  deleteByCampaignId,
};

async function getAll() {
  return await db.KindDonation.findAll();
}

async function getAll(query, campaign_id) {
  try {
    const whereClause = {};
    if (campaign_id) {
      whereClause.campaign_id = campaign_id;
    }

    const records = await db.KindDonation.findAll({
      where: whereClause,
    });

    return records;
  } catch (error) {
    throw error;
  }
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const record = await db.KindDonation.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

async function deleteByCampaignId(campaign_id) {
  try {
    await db.KindDonation.destroy({
      where: {
        campaign_id: campaign_id,
      },
    });
    return { message: "All records deleted successfully" };
  } catch (error) {
    throw error;
  }
}
