﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const newsletterService = require("./newsletter.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  newsletterService
    .create(req.body)
    .then(() => {
      logRequest(
        req,
        `Created a new newsletter subscription for ${req.body.email}`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  const { pageName } = req.query;
  newsletterService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all newsletters", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  newsletterService
    .getById(req.params.id)
    .then((record) => {
      logRequest(
        req,
        `Fetched newsletter with email: ${record?.email}`,
        "READ"
      );
      res.json(record);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    email: Joi.string().required(),
    description: Joi.string().allow(null, ""),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  newsletterService
    .update(req.params.id, req.body)
    .then((record) => {
      logRequest(req, `Updated newsletter with ID: ${req.params.id}`, "UPDATE");
      res.json(record);
    })
    .catch(next);
}

function _delete(req, res, next) {
  newsletterService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted newsletter with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
