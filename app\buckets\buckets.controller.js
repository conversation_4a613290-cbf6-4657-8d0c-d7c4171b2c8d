const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const bucketService = require("./bucket.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", create);
router.post("/getRecommendedBuckets", getRecommendedBuckets);
router.get("/:id", getById);
router.put("/:id", update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  bucketService
    .create(req.body)
    .then((bucket) => {
      logRequest(req, `Created a new bucket ${bucket?.name}`, "CREATE");
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getRecommendedBuckets(req, res, next) {
  bucketService
    .getRecommendedBuckets(req.body)
    .then((buckets) => {
      logRequest(req, "Fetched recommended buckets", "READ");
      res.json({
        status: true,
        message: "Buckets fetched successfully",
        data: buckets,
      });
    })
    .catch(next);
}

function getAll(req, res, next) {
  bucketService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, `Fetched all buckets`, "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  bucketService
    .getById(req.params.id)
    .then((record) => {
      logRequest(req, `Fetched bucket ${record?.name}`, "READ");
      res.json(record);
    })
    .catch(next);
}

function update(req, res, next) {
  bucketService
    .update(req.params.id, req.body)
    .then((record) => {
      logRequest(req, `Updated bucket ${record?.name}`, "UPDATE");
      res.json(record);
    })
    .catch(next);
}

function _delete(req, res, next) {
  bucketService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted bucket with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
