const { where } = require("sequelize");
const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  getByProductId,
  deleteByProductId,
};

db.ProductImage.belongsTo(db.Product, {
  as: "productInfo",
  through: "products",
  foreignKey: "product_id",
  otherKey: "product_id",
});

async function getAll(productId) {
  let where = {};
  if (productId) {
    where.product_id = productId;
  }
  const result = await db.ProductImage.findAll({
    where: where,
    include: [
      {
        model: db.Product,
        as: "productInfo",
        attributes: ["id", "name"],
      },
    ],
  });
  if (!result) return "result not found for this status";
  return result;
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const record = await db.ProductImage.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.ProductImage.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}

async function getByProductId(product_id) {
  const record = await db.ProductImage.findAll({
    where: {
      product_id,
    },
  });
  if (!record) throw "Record not found";
  return record;
}
async function deleteByProductId(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}
