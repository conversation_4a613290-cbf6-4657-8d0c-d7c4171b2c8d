﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const campaignService = require("./campaign.service");
const uploadFile = require("../_middleware/upload");
const { logAction } = require("../_helpers/logger");

const multer = require("multer");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/campaign-cover-images";
    cb(null, __basedir + "uploads/campaign-cover-images/");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

const uploadConfig = multer({
  storage: storage,
});

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.get("/status/:statusParam", getCampaignsByStatusParam);
router.get("/searchByNgoName", getCampaignsByNgoName);
router.get("/search", getCampaigns);
router.get("/:id", getById);
router.get("/stats/campaignStats", getCampaignStats);
router.get("/stats/fullcampaignStats", getFullCampaignStats);
router.get("/getUpcomingCampaigns/details", getUpcomingCampaigns);

router.post(
  "/searchByLocationOrCategoryOrSkills",
  getCampaignsByLocationOrCategoryOrSkills
);
router.post("/getAllCampaigns", getCampaignsByFilters);
router.post("/by-categories", getByCategoryIds);

router.post("/", uploadConfig.single("file"), create);
router.put("/:id", uploadConfig.single("file"), update);
router.patch("/:id", patch);
router.patch("/mark/featured", markAsFeatured);

router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  campaignService
    .create(req.body)
    .then((record) => {
      logRequest(req, `Created a new campaign: ${record?.name}`, "CREATE");
      res.json({
        status: true,
        message: "Record created successfully",
        data: record,
      });
    })
    .catch(next);
}

function getCampaignsByFilters(req, res, next) {
  const filters = req.body;

  if (filters.latitude && isNaN(parseFloat(filters.latitude))) {
    return res.status(400).json({
      status: false,
      message: "Latitude must be a valid number",
    });
  }

  if (filters.longitude && isNaN(parseFloat(filters.longitude))) {
    return res.status(400).json({
      status: false,
      message: "Longitude must be a valid number",
    });
  }

  campaignService
    .getCampaignsByFilters(filters, req.query)
    .then((ngos) => {
      logRequest(req, "Fetched Campaigns by Filters", "READ");
      res.json(ngos);
    })
    .catch(next);
}

function getAll(req, res, next) {
  campaignService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all campaigns", "READ");
      res.json(records);
    })
    .catch(next);
}
function getUpcomingCampaigns(req, res, next) {
  campaignService
    .getUpcomingCampaigns(req.query)
    .then((records) => {
      logRequest(req, "Fetched upcoming campaigns", "READ");
      res.json(records);
    })
    .catch(next);
}

function getByCategoryIds(req, res, next) {
  const { categoryIds } = req.body;

  campaignService
    .getByCategoryIds(categoryIds)
    .then((records) => {
      logRequest(req, "Fetched campaigns by category IDs", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  campaignService
    .getById(req.params.id, req.query)
    .then((record) => {
      logRequest(req, `Fetched campaign: ${record?.name}`, "READ");
      res.json(record);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().allow(null, ""),
    description: Joi.string().allow(null, ""),
    campaign_start_date: Joi.date().allow(null, ""),
    event_time: Joi().allow(null, ""),
    campaign_end_date: Joi.string().allow(null, ""),
    sameday_event: Joi.string().allow(null, ""),
    category_id: Joi.number().allow(null, ""),
    format: Joi.string().allow(null, ""),
    ngo_id: Joi.number().allow(null, ""),
    city: Joi.string().allow(null, ""),
    pincode: Joi.string().allow(null, ""),
    state: Joi.string().allow(null, ""),
    meeting_link: Joi.string().allow(null, ""),
    latitude: Joi.number().precision(8).allow(null, ""),
    longitude: Joi.number().precision(8).allow(null, ""),
    facebook_url: Joi.string().allow(null, ""),
    instagram_url: Joi.string().allow(null, ""),
    youtube_url: Joi.string().allow(null, ""),
    twitter_url: Joi.string().allow(null, ""),
    promotional_hashtag: Joi.string().allow(null, ""),
    address: Joi.string().allow(null, ""),
    country: Joi.string().allow(null, ""),
    impact_goal: Joi.string().allow(null, ""),
    fund_raising_target: Joi.number().allow(null, ""),
    donor_target_type: Joi.string().allow(null, ""),
    volunteers_required: Joi.string().allow(null, ""),
    no_of_volunteers: Joi.number().allow(null, ""),
    skills: Joi.string().allow(null, ""),
    createdBy: Joi.number().required(),
    status: Joi.string().allow(null, ""),
    fileName: Joi.string().allow(null, ""),
    ratio: Joi.string().allow(null, ""),
    updatedBy: Joi.number().allow(null, ""),
  });

  validateRequest(req, next, schema);
}

function update(req, res, next) {
  campaignService
    .update(req.params.id, req.body)
    .then((record) => {
      logRequest(req, `Updated campaign: ${record?.name}`, "UPDATE");
      res.json(record);
    })
    .catch(next);
}

function patch(req, res, next) {
  campaignService
    .patch(req.params.id, req.body)
    .then((record) => {
      logRequest(req, `Patched campaign status: ${record?.name}`, "UPDATE");
      res.json(record);
    })
    .catch(next);
}

function _delete(req, res, next) {
  campaignService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted campaign with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
function getCampaignsByStatusParam(req, res, next) {
  const statusParam = req.params.statusParam;
  const { ngoId, pageName, page, limit, portalUserId, searchTerm } = req.query;

  campaignService
    .getCampaignsByStatusParam(
      statusParam,
      ngoId,
      page,
      limit,
      portalUserId,
      searchTerm,
      req.query
    )
    .then((campaigns) => {
      logRequest(
        req,
        `Fetched campaigns by status: ${req.params.statusParam}`,
        "READ"
      );
      return res.json(campaigns);
    })
    .catch(next);
}

function getCampaignsByNgoName(req, res, next) {
  const ngoName = req.query.ngoName;
  if (!ngoName) {
    return res
      .status(400)
      .json({ status: false, message: "NGO name is required for search" });
  }

  campaignService
    .getCampaignsByNgoName(req.query.ngoName)
    .then((records) => {
      logRequest(
        req,
        `Fetched campaigns by NGO name: ${req.query.ngoName}`,
        "READ"
      );
      res.json(records);
    })
    .catch(next);
}

// function getCampaignsByLocationOrCategoryOrSkills(req, res, next) {
//   const { latitude, longitude, radius, categoryId, skills } = req.query;

//   // Ensure latitude and longitude are provided for location-based search
//   if (!latitude || !longitude) {
//     return res.status(400).json({
//       status: false,
//       message: "Latitude and longitude are required for location-based search",
//     });
//   }

//   campaignService
//     .getCampaignsByLocationOrCategoryOrSkills({
//       latitude: parseFloat(latitude),
//       longitude: parseFloat(longitude),
//       radius: parseFloat(radius) || 10, // Default radius is 10 km
//       categoryId: categoryId ? parseInt(categoryId, 10) : null,
//       skills,
//     })
//     .then((campaigns) => res.json(campaigns))
//     .catch(next);
// }

function getCampaignsByLocationOrCategoryOrSkills(req, res, next) {
  const filters = req.body;

  if (filters.latitude && isNaN(parseFloat(filters.latitude))) {
    return res.status(400).json({
      status: false,
      message: "Latitude must be a valid number",
    });
  }

  if (filters.longitude && isNaN(parseFloat(filters.longitude))) {
    return res.status(400).json({
      status: false,
      message: "Longitude must be a valid number",
    });
  }

  campaignService
    .getCampaignsByLocationOrCategoryOrSkills(req.body)
    .then((records) => {
      logRequest(req, "Fetched campaigns by location/category/skills", "READ");
      res.json(records);
    })
    .catch(next);
}

function getCampaignStats(req, res, next) {
  campaignService
    .getCampaignStats(req.query.ngo_id)
    .then((stats) => {
      logRequest(req, "Fetched campaign stats", "READ");
      res.json(stats);
    })
    .catch(next);
}
function getFullCampaignStats(req, res, next) {
  campaignService
    .getFullCampaignStats()
    .then((stats) => {
      logRequest(req, "Fetched full campaign stats", "READ");
      res.json(stats);
    })
    .catch(next);
}

function markAsFeatured(req, res, next) {
  campaignService
    .markAsFeatured(req.query.campaignId, req.query.featureMode)
    .then(() => {
      logRequest(
        req,
        `Marked campaign ${req.query.campaignId} as featured`,
        "UPDATE"
      );
      res.json({
        statusText: "OK",
        message: "Campaign feature status updated successfully",
      });
    })
    .catch(next);
}
function getCampaigns(req, res, next) {
  campaignService
    .getCampaigns(req.query)
    .then((records) => {
      logRequest(req, "Fetched all campaigns", "READ");
      res.json(records);
    })
    .catch(next);
}
