﻿const { Op } = require("sequelize");
const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
};

async function getAll(params, isadmin) {
  let where = {};

  if (params && params?.documentType && params?.documentType !== "undefined") {
    where = {
      ...where,
      document_type: {
        [Op.like]: `%${params?.documentType}%`,
      },
    };
  }
  if (isadmin !== "yes") {
    where = {
      ...where,
      status: "Active",
    };
  }

  return await db.Question.findAll({ where: where, order: [["id", "DESC"]] });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const record = await db.Question.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // params.slug = utils.generateSlug(params.name);
  // // validate

  // const recordChanged = params.slug && record.slug !== params.slug;
  // if (
  //   recordChanged &&
  //   (await db.Question.findOne({ where: { slug: params.slug } }))
  // ) {
  //   throw 'Question "' + params.name + '" is already taken';
  // }

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.Question.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
