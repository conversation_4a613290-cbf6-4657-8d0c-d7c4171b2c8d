const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    description: { type: DataTypes.TEXT, allowNull: false },
    fileName: { type: DataTypes.STRING, allowNull: false },
    collection_date: { type: DataTypes.DATE, allowNull: false },
    portal_user_id: { type: DataTypes.INTEGER, allowNull: false },
    ngo_id: { type: DataTypes.INTEGER, allowNull: true },
    campaign_id: { type: DataTypes.INTEGER, allowNull: false },
    createdAt: { type: DataTypes.DATE, allowNull: false },
    updatedAt: { type: DataTypes.DATE, allowNull: false },
  };

  const options = {
    defaultScope: {
      // exclude hash by default
    },
    scopes: {
      // include hash with this scope
      withHash: { attributes: {} },
    },
  };

  return sequelize.define("campaign_milestones", attributes, options);
}
