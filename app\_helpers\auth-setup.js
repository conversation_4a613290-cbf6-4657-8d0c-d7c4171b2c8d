// auth-setup.js
const { google } = require("googleapis");
const fs = require("fs");
const readline = require("readline");

const config = require("../../config.json");

const oauth2Client = new google.auth.OAuth2(
  config.GOOGLE_API_CLIENT_ID,
  config.GOOGLE_API_CLIENT_SECRET,
  config.GOOGLE_API_REDIRECT_URI
);
const SCOPES = [
  "https://www.googleapis.com/auth/photoslibrary.appendonly",
  "https://www.googleapis.com/auth/photoslibrary.readonly",
  "https://www.googleapis.com/auth/photoslibrary",
];

const authUrl = oauth2Client.generateAuthUrl({
  access_type: "offline",
  scope: SCOPES,
});

console.log("Authorize this app by visiting:", authUrl);

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

rl.question("Enter the code from that page here: ", async (code) => {
  const { tokens } = await oauth2Client.getToken(code);
  fs.writeFileSync("token.json", JSON.stringify(tokens));
  console.log("Token stored to token.json");
  rl.close();
});
