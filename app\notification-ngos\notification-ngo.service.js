﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const { Op } = require("sequelize");
const config = require("../../config.json");


module.exports = {
  getAll,
  getAllBySenderId,
  getById,
  create,
  update,
  delete: _delete,
};

// admin.initializeApp({
//   credential: admin.credential.cert({
//     projectId: config.project_id,
//     clientEmail: config.client_email,
//     privateKey: config.private_key.replace(/\\n/g, "\n"),
//   }),
// });

async function getAll(params) {
  const { ngoId, commnetType, typeId, userId } = params;
  let where = {};

  if (userId) {
    // Override normal filtering if userId is provided
    where = {
      [Op.or]: [{ type: "all" }, { type: "user", type_id: userId }],
    };
  }

  if (ngoId) {
    where.ngo_id = ngoId;
  }
  if (commnetType && commnetType !== "null" && commnetType !== "undefined") {
    where.type = params.commnetType;
  }

  if (typeId && typeId !== "undefined") {
    where.type_id = typeId;
  }
  const result = await db.NotificationNgo.findAll({
    where: where,
    order: [["createdAt", "DESC"]],
  });
  if (!result) return "result not found for this status";
  return result;
}

async function getAllBySenderId(senderId) {
  let where = {};
  if (senderId) {
    where.sender_id = senderId;
  }
  const result = await db.NotificationNgo.findAll({
    where: where,
  });
  if (!result) return "result not found for this sender";
  return result;
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const record = await db.NotificationNgo.create(params);
  //   const ngoRecord = await db.Ngo.findOne({
  //     where: {
  //       id: record?.type_id,
  //     },
  //   });

  //   const emailTemplate = await db.CommunicationEmail.findOne({
  //     where: {
  //       id: 2,
  //     },
  //   });
  //   const fullName = `${record.fullname}`;
  //   const emailBody = utils.replacePlaceholders(emailTemplate.template, {
  //     fullname: fullName,
  //   });

  //   const emailStatus = await utils.sendEmail(
  //     `${ngoRecord?.email}`,
  //     `${emailTemplate.subject}`,
  //     emailBody
  //   );
  //   if (!emailStatus) {
  //     throw "We were unable to send the email. Please retry again.";
  //   } else {
  return record;
  //   }
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.NotificationNgo.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}

