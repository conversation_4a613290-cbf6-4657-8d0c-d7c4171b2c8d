const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const rsvpInformationService = require("./rsvp-information.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  rsvpInformationService
    .create(req.body)
    .then((rsvp) => {
      logRequest(
        req,
        `Created RSVP with campaign ID: ${rsvp?.campaign_id}, NGO ID: ${rsvp?.ngo_id}, RSVP Value: ${rsvp?.rsvp_value}`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  rsvpInformationService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all RSVPs", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  rsvpInformationService
    .getById(req.params.id)
    .then((rsvp) => {
      logRequest(
        req,
        `Fetched RSVP with campaign ID: ${rsvp?.campaign_id}, NGO ID: ${rsvp?.ngo_id}, RSVP Value: ${rsvp?.rsvp_value}`,
        "READ"
      );
      res.json(rsvp);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    campaign_id: Joi.number().integer().required(),
    ngo_id: Joi.number().integer().required(),
    message: Joi.string().required(),
    rsvp_value: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  rsvpInformationService
    .update(req.params.id, req.body)
    .then((rsvp) => {
      logRequest(
        req,
        `Updated RSVP with campaign ID: ${rsvp?.campaign_id}, NGO ID: ${rsvp?.ngo_id}, RSVP Value: ${rsvp?.rsvp_value}`,
        "UPDATE"
      );
      res.json(rsvp);
    })
    .catch(next);
}

function _delete(req, res, next) {
  rsvpInformationService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted RSVP with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
