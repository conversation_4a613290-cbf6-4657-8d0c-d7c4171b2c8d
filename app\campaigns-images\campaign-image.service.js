const { where } = require("sequelize");
const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  getBycampaignId,
  deleteBycampaignId,
  updatePromoImages,
};

db.CampaignImage.belongsTo(db.Ngo, {
  as: "campaignInfo",
  through: "campaigns",
  foreignKey: "campaign_id",
  otherKey: "campaign_id",
});

async function getAll(campaignId) {
  let where = {};
  if (campaignId) {
    where.campaign_id = campaignId;
  }
  const result = await db.CampaignImage.findAll({
    where: where,
    inclde: [
      {
        model: db.Campaigns,
        as: "campaignsInfo",
        attributes: ["id", "name"],
      },
    ],
  });
  if (!result) return "result not found for this status";
  return result;
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const record = await db.CampaignImage.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  Object.assign(record, params);
  await record.save();

  return record.get();
}
async function updatePromoImages(id, params) {
  // Delete all existing records for this campaign_id
  await db.CampaignImage.destroy({
    where: { campaign_id: id },
  });

  // Create new records based on incoming params
  const newRecords = await Promise.all(
    params.map((item) =>
      db.CampaignImage.create({
        fileName: item.fileName,
        description: null,
        campaign_id: id,
        type: item.ngo_id || item.type == "promo" ? "promo" : "categories",
        markedForDeletion: item.markedForDeletion,
        priority: item.priority,
      })
    )
  );

  return newRecords;
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.CampaignImage.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}

async function getBycampaignId(campaign_id) {
  const record = await db.CampaignImage.findAll({
    where: {
      campaign_id,
    },
  });
  if (!record) throw "Record not found";
  return record;
}
async function deleteBycampaignId(id) {
  const record = await getSingleRecord(id);

  await record.destroy();
}
