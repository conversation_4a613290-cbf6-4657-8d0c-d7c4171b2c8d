const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const { Sequelize } = require("sequelize");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
};

db.Auditlog.belongsTo(db.PortalUser, {
  as: "userInfo",
  through: "portal_users",
  foreignKey: "userId",
  otherKey: "userId",
});

async function getAll(query) {
  const page = query.page ? parseInt(query.page) : 1;
  const limit = query.limit ? parseInt(query.limit) : 10;
  const offset = (page - 1) * limit;
  const search = query.search ? query.search.trim() : "";

  // Construct where condition for search
  let whereCondition = {};
  if (search) {
    whereCondition = {
      "$userInfo.fullname$": { [Sequelize.Op.like]: `%${search}%` }, // Case-insensitive search
    };
  }

  const { count, rows } = await db.Auditlog.findAndCountAll({
    where: whereCondition,
    order: [["id", "DESC"]],
    limit: limit,
    offset: offset,
    include: [
      {
        model: db.PortalUser,
        as: "userInfo",
        attributes: ["id", "fullname"],
      },
    ],
  });

  return {
    totalRecords: count,
    totalPages: Math.ceil(count / limit),
    currentPage: page,
    records: rows,
  };
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // validate
  const record = await db.Auditlog.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);
  // copy params to Auditlog and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.Auditlog.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
