const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    start: { type: DataTypes.INTEGER, allowNull: false },
    end: { type: DataTypes.INTEGER, allowNull: false },
    statecode: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
    },
  };

  const options = {
    defaultScope: {
      // exclude hash by default
    },
    scopes: {
      // include hash with this scope
      withHash: { attributes: {} },
    },
  };

  return sequelize.define("dynamic_adds", attributes, options);
}
