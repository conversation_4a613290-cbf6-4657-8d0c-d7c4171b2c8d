﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
};

async function getAll(params) {
  const whereClause = {};

  if (params?.campaignId) {
    whereClause.campaign_id = params.campaignId;
  }

  if (params?.causeId) {
    whereClause.cause_id = params.causeId;
  }

  if (params?.orderId) {
    whereClause.order_id = params.orderId;
  }

  const orderCampaigns = await db.OrderCampaign.findAll({
    where: whereClause,
    order: [["id", "DESC"]],
  });

  return orderCampaigns;
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const record = await db.OrderCampaign.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
  return record;
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.OrderCampaign.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
