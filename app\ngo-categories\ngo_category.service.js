const { where } = require("sequelize");
const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  getByNGOId,
  deleteByNGOId,
};

db.NgoCategory.belongsTo(db.Category, {
  as: "categoryInfo",
  through: "categories",
  foreignKey: "category_id",
  otherKey: "category_id",
});

async function getAll(params) {
  const whereClause = {};
  if (params.ngoId) {
    whereClause.ngo_id = params.ngoId;
  }
  return await db.NgoCategory.findAll({
    where: whereClause,
    order: [["id", "DESC"]],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // validate
  // params.slug = utils.generateSlug(params.name);
  // if (await db.NgoCategory.findOne({ where: { slug: params.slug } })) {
  //   throw 'Record "' + params.name + '" is already taken';
  //   return;
  // }

  const record = await db.NgoCategory.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // params.slug = utils.generateSlug(params.name);
  // validate

  const recordChanged = params.slug && record.slug !== params.slug;
  // if (
  //   recordChanged &&
  //   (await db.NgoCategory.findOne({ where: { slug: params.slug } }))
  // ) {
  //   throw 'NgoCategory "' + params.name + '" is already taken';
  // }

  // copy params to NgoCategory and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.NgoCategory.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}

async function getByNGOId(ngo_id) {
  const record = await db.NgoCategory.findAll({
    where: {
      ngo_id,
    },
    include: [
      {
        model: db.Category,
        as: "categoryInfo",
        attributes: ["id", "name"],
      },
    ],
  });
  if (!record) throw "Record not found";
  return record;
}
async function deleteByNGOId(ngo_id) {
  const records = await db.NgoCategory.findAll({
    where: {
      ngo_id,
    },
  });

  if (records.length !== 0) {
    // Use Promise.all to delete all records concurrently
    await Promise.all(records.map((record) => record.destroy()));
  }
}
