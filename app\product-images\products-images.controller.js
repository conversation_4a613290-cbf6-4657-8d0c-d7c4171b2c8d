const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const productImageService = require("./product-image.service");
const { logAction } = require("../_helpers/logger");

const multer = require("multer");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/product-images";
    cb(null, __basedir + "uploads/product-images/");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

const uploadConfig = multer({
  storage: storage,
});

// routes
router.get("/:productId", getAll);
router.post("/", uploadConfig.single("file"), updateSchema, create);
// router.get("/:id", getById);
router.put("/:id", uploadConfig.single("file"), updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  productImageService
    .create(req.body)
    .then(() =>
      res.json({ status: true, message: "Record created successfully" })
    )
    .catch(next);
}

function getAll(req, res, next) {
  const productId = req.params.productId;

  productImageService
    .getAll(productId)
    .then((records) => {
     
      return res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  productImageService
    .getById(req.params.id)
    .then((portalUserCategory) => res.json(portalUserCategory))
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    fileName: Joi.string().required(),
    product_id: Joi.number().required(),
    description: Joi.string().optional().allow("", null),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  productImageService
    .update(req.params.id, req.body)
    .then((portalUserCategory) => res.json(portalUserCategory))
    .catch(next);
}

function _delete(req, res, next) {
  productImageService
    .deleteByProductId(req.params.id)
    .then(() =>
      res.json({ status: true, message: "Records deleted successfully" })
    )
    .catch(next);
}
