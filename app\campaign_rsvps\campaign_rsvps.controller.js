const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const campaignRsvpsService = require("./campaign_rsvp.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.get("/events/upcoming", getUpcomingUserEventsForUser);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  campaignRsvpsService
    .create(req.body)
    .then(() => {
      logRequest(req, `Created a new campaign RSVP`, "CREATE");
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  campaignRsvpsService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, `Fetched all campaign RSVPs`, "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  campaignRsvpsService
    .getById(req.params.id)
    .then((campaignRsvp) => {
      logRequest(
        req,
        `Fetched campaign RSVP with ID: ${req.params.id}`,
        "READ"
      );
      res.json(campaignRsvp);
    })
    .catch(next);
}

function getUpcomingUserEventsForUser(req, res, next) {

  campaignRsvpsService
    .getUpcomingUserEventsForUser(req.query)
    .then((records) => {
      res.json(records);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    campaign_id: Joi.number().required(),
    user_id: Joi.number().required(),
    rsvp_value: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  campaignRsvpsService
    .update(req.params.id, req.body)
    .then((campaignRsvp) => {
      logRequest(
        req,
        `Updated campaign RSVP with ID: ${req.params.id}`,
        "UPDATE"
      );
      res.json(campaignRsvp);
    })
    .catch(next);
}

function _delete(req, res, next) {
  campaignRsvpsService
    .delete(req.params.id)
    .then(() => {
      logRequest(
        req,
        `Deleted campaign RSVP with ID: ${req.params.id}`,
        "DELETE"
      );
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
