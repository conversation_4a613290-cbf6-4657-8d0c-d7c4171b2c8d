const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    order_id: { type: DataTypes.INTEGER, allowNull: false },
    ngo_id: { type: DataTypes.INTEGER, allowNull: false },
    cause_id: { type: DataTypes.INTEGER, allowNull: false },
  };

  const options = {
    defaultScope: {
      // exclude hash by default
    },
    scopes: {
      // include hash with this scope
      withHash: { attributes: {} },
    },
  };

  return sequelize.define("order_ngos", attributes, options);
}
