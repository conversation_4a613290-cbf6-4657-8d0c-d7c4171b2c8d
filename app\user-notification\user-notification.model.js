const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    title: { type: DataTypes.STRING, allowNull: false },
    body: { type: DataTypes.TEXT, allowNull: false },
    category_id: { type: DataTypes.INTEGER, allowNull: true },
    type: { type: DataTypes.STRING, allowNull: true },
    type_id: { type: DataTypes.INTEGER, allowNull: true },
  };

  const options = {
    defaultScope: {},
    scopes: {
      withDetails: { attributes: {} },
    },
  };

  return sequelize.define("user_notifications", attributes, options);
}
