const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const notificationsService = require("./notification-ngo.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.get("/sender/:senderId", getAllBySenderId);
router.post("/", updateSchema, create);
// router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.patch("/:id", updateReadFlag);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  notificationsService
    .create(req.body)
    .then((record) => {
      logRequest(
        req,
        `Created a new notification with description: ${record?.description}, type_id: ${record?.type_id}, type: ${record?.type}`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  notificationsService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all notifications", "READ");
      res.json(records);
    })
    .catch(next);
}
function getAllBySenderId(req, res, next) {
  const senderId = req.params.senderId;
  notificationsService
    .getAllBySenderId(senderId)
    .then((records) => {
      logRequest(req, "Fetched all notifications by sender ID", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  notificationsService
    .getById(req.params.id)
    .then((user) => res.json(user))
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    description: Joi.string().required(),
    ngo_id: Joi.number().optional().allow("", null),
    type_id: Joi.number().required(),
    sender_id: Joi.number().required(),
    messageRead: Joi.string().required(),
    type: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  notificationsService
    .update(req.params.id, req.body)
    .then((record) => {
      logRequest(
        req,
        `Updated notification with description: ${record?.description}, type_id: ${record?.type_id}, type: ${record?.type}`,
        "UPDATE"
      );
      res.json(record);
    })
    .catch(next);
}
function updateReadFlag(req, res, next) {
  notificationsService
    .update(req.params.id, req.body)
    .then((record) => {
      logRequest(
        req,
        `Updated read flag for notification with description: ${record?.description}, type_id: ${record?.type_id}, type: ${record?.type}`,
        "UPDATE"
      );
      res.json(record);
    })
    .catch(next);
}

function _delete(req, res, next) {
  notificationsService
    .delete(req.params.id)
    .then(() => {
      logRequest(
        req,
        `Deleted notification with ID: ${req.params.id}`,
        "DELETE"
      );
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
