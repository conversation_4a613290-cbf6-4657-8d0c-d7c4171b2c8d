﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const orderCampaignsService = require("./order-campaign.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  orderCampaignsService
    .create(req.body)
    .then(() => {
      logRequest(req, `Created a new orderCampaign`, "CREATE");
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  orderCampaignsService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, `Fetched all orderCampaign`, "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  orderCampaignsService
    .getById(req.params.id)
    .then((record) => {
      logRequest(req, `Fetched orderCampaign ${record?.name}`, "READ");
      res.json(record);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    order_id: Joi.number().required(),
    campaign_id: Joi.number().required(),
    cause_id: Joi.number().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  orderCampaignsService
    .update(req.params.id, req.body)
    .then((record) => {
      logRequest(req, `Updated orderCampaign `, "UPDATE");
      res.json(record);
    })
    .catch(next);
}

function _delete(req, res, next) {
  orderCampaignsService
    .delete(req.params.id)
    .then(() => {
      logRequest(
        req,
        `Deleted orderCampaign with ID: ${req.params.id}`,
        "DELETE"
      );
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
