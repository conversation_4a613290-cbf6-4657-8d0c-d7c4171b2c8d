// const jwt = require('express-jwt');
const jwt = require("jsonwebtoken");
const { secret } = require("../../config.json");
const db = require("../_helpers/db");

module.exports = authorize;

function authorize() {
  return [
    // authenticate JW<PERSON> token and attach decoded token to request as req.user
    jwt({ secret, algorithms: ["HS256"] }),

    // attach full user record to request object
    async (req, res, next) => {
      // get user with id from token 'sub' (subject) property
      const portalUser = await db.PortalUser.findByPk(req.user.sub);
      const user = await db.User.findByPk(req.user.sub);
      // check user still exists
      if (!user && !portalUser)
        return res.status(401).json({ message: "Unauthorized" });

      // authorization successful
      if (user) {
        req.user = user.get();
      }
      if (portalUser) {
        req.user = portalUser.get();
      }
      next();
    },
  ];
}
