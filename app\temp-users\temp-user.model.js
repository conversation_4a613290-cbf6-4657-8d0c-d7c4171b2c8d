const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    mobile_number: { type: DataTypes.STRING, allowNull: true },
    otp: { type: DataTypes.STRING, allowNull: true },
    attempt_count: { type: DataTypes.INTEGER, defaultValue: 0 },
    last_attempt_date: { type: DataTypes.DATEONLY, allowNull: true },
    is_blocked: { type: DataTypes.STRING, defaultValue: false },
  };

  const options = {
    defaultScope: {
      // exclude hash by default
    },
    scopes: {
      // include hash with this scope
      withHash: { attributes: {} },
    },
  };

  return sequelize.define("temp_users", attributes, options);
}
