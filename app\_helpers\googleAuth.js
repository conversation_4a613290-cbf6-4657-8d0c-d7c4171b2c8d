// googleAuth.js
const { google } = require("googleapis");
const fs = require("fs");
const path = require("path");
const config = require("../../config.json")

const TOKEN_PATH = path.join(__dirname, "token.json"); // Save tokens here

const oauth2Client = new google.auth.OAuth2(
  config.GOOGLE_API_CLIENT_ID,
  config.GOOGLE_API_CLIENT_SECRET,
  config.GOOGLE_API_REDIRECT_URI
);

function getAuthorizedClient() {
  const token = JSON.parse(fs.readFileSync(TOKEN_PATH, "utf8"));
  oauth2Client.setCredentials(token);

  // Auto-save updated tokens when refreshed
  oauth2Client.on("tokens", (newTokens) => {
    if (newTokens.refresh_token) {
      token.refresh_token = newTokens.refresh_token;
    }
    token.access_token = newTokens.access_token;
    fs.writeFileSync(TOKEN_PATH, JSON.stringify(token));
  });

  return oauth2Client;
}

async function getAccessToken() {
  const client = getAuthorizedClient();
  const { token } = await client.getAccessToken();
  return token;
}

module.exports = {
  getAccessToken,
};
