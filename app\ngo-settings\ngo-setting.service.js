const { where } = require("sequelize");
const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  getByNGOId,
  deleteByNGOId,
};

async function getAll(ngoId) {
  let where = {};
  if (ngoId) {
    where.ngo_id = ngoId;
  }
  const result = await db.NgoSetting.findAll({
    where: where,
  });
  if (!result) return "result not found for this status";
  return result;
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(ngoId, settings) {
  const payloadWithNgoId = settings.map((setting) => ({
    ...setting,
    ngo_id: ngoId,
  }));
  const record = await db.NgoSetting.bulkCreate(payloadWithNgoId);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // params.slug = utils.generateSlug(params.name);
  // validate

  const recordChanged = params.slug && record.slug !== params.slug;
  // if (
  //   recordChanged &&
  //   (await db.NgoSetting.findOne({ where: { slug: params.slug } }))
  // ) {
  //   throw 'NgoSetting "' + params.name + '" is already taken';
  // }

  // copy params to NgoSetting and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.NgoSetting.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}

async function getByNGOId(ngo_id) {
  const record = await db.NgoSetting.findAll({
    where: {
      ngo_id,
    },
  });
  if (!record) throw "Record not found";
  return record;
}
async function deleteByNGOId(ngo_id) {
  const records = await db.NgoSetting.findAll({
    where: {
      ngo_id,
    },
  });

  if (records.length !== 0) {
    // Use Promise.all to delete all records concurrently
    await Promise.all(records.map((record) => record.destroy()));
  }
}
