﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const orderNgosService = require("./temp-user.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", create);
router.get("/:id", getById);
router.put("/:id", update);
router.delete("/:id", _delete);
router.post("/sendVerificationOTP", sendVerificationOTP);
router.post("/verifyOtp", verifyOtp);
router.post("/verifyPan", verifyPan);

module.exports = router;

function create(req, res, next) {
  orderNgosService
    .create(req.body)
    .then(() => {
      logRequest(req, `Created a new orderNgos`, "CREATE");
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function sendVerificationOTP(req, res, next) {
  orderNgosService
    .sendVerificationOTP(req.query)
    .then((resObj) => res.json(resObj))
    .catch(next);
}

function verifyOtp(req, res, next) {
  orderNgosService
    .verifyOtp(req.body)
    .then((resObj) => res.json(resObj))
    .catch(next);
}
function verifyPan(req, res, next) {
  orderNgosService
    .verifyPan(req.body)
    .then((resObj) => res.json(resObj))
    .catch(next);
}

function getAll(req, res, next) {
  orderNgosService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, `Fetched all orderNgos`, "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  orderNgosService
    .getById(req.params.id)
    .then((record) => {
      logRequest(req, `Fetched orderNgos ${record?.name}`, "READ");
      res.json(record);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    order_id: Joi.number().required(),
    ngo_id: Joi.number().required(),
    cause_id: Joi.number().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  orderNgosService
    .update(req.params.id, req.body)
    .then((record) => {
      logRequest(req, `Updated orderNgos `, "UPDATE");
      res.json(record);
    })
    .catch(next);
}

function _delete(req, res, next) {
  orderNgosService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted orderNgos with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
