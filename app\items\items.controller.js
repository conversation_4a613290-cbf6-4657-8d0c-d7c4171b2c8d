﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const itemService = require("./item.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  itemService
    .create(req.body)
    .then((item) => {
      logRequest(req, `Created a new item: ${item?.name}`, "CREATE");
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  itemService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, `Fetched all items`, "READ");
      return res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  itemService
    .getById(req.params.id)
    .then((item) => {
      logRequest(req, `Fetched item: ${item?.name}`, "READ");
      res.json(item);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    description: Joi.string().required(),
    status: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  itemService
    .update(req.params.id, req.body)
    .then((item) => {
      logRequest(req, `Updated item: ${item?.name}`, "UPDATE");
      res.json(item);
    })
    .catch(next);
}

function _delete(req, res, next) {
  itemService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted item with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
