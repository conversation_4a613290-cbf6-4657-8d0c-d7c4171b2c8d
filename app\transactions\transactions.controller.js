﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const authorize = require("../_middleware/authorize");
const transactionService = require("./transaction.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.get("/getDonationSummary/byUserId", getDonationSummary);
router.get("/getTransactionDetails/byTransactionId", getTransactionDetails);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  transactionService
    .create(req.body)
    .then(() => {
      logRequest(
        req,
        `Created a new transaction: User ID: ${req.body.user_id}, Amount: ${req.body.amount}, Order ID: ${req.body.order_id}`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  const { page, limit } = req.query;

  transactionService
    .getAll(req.query, page, limit)
    .then((records) => {
      logRequest(req, "Fetched all transactions", "READ");
      res.json(records);
    })
    .catch(next);
}
function getDonationSummary(req, res, next) {
  transactionService
    .getDonationSummary(req.query)
    .then((records) => {
      logRequest(req, "Fetched all transactions", "READ");
      res.json(records);
    })
    .catch(next);
}
function getTransactionDetails(req, res, next) {
  transactionService
    .getTransactionDetails(req.query)
    .then((records) => {
      logRequest(req, "Fetched all transactions", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  transactionService
    .getById(req.params.id)
    .then((transaction) => {
      logRequest(
        req,
        `Fetched transaction: User ID: ${transaction?.user_id}, Amount: ${transaction?.amount}, Order ID: ${transaction?.order_id}`,
        "READ"
      );
      res.json(transaction);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    user_id: Joi.number().integer().required(),
    amount: Joi.number().precision(2).required(),
    order_id: Joi.string().max(50).optional(),
    razorpay_payment_id: Joi.string().max(100).optional(),
    razorpay_signature: Joi.string().max(100).optional(),
    status: Joi.string().max(30).optional(),
    tracking_id: Joi.string().max(100).optional(),
    payment_mode: Joi.string().max(50).optional(),
    bank_ref_no: Joi.string().max(255).optional(),
    all_values: Joi.string().optional(),
    transferResponse: Joi.string().optional(),
    payment_type: Joi.string().max(200).optional(),
    donation_type: Joi.string().valid("kind", "amount", "donation").required(),
    ngo_id: Joi.number().integer().optional(),
    campaign_id: Joi.number().integer().optional(),
    description: Joi.string().optional(),
    impact_created: Joi.number().optional(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  transactionService
    .update(req.params.id, req.body)
    .then((transaction) => {
      logRequest(
        req,
        `Updated transaction: User ID: ${transaction?.user_id}, Amount: ${transaction?.amount}, Order ID: ${transaction?.order_id}`,
        "UPDATE"
      );
      res.json(transaction);
    })
    .catch(next);
}

function _delete(req, res, next) {
  transactionService
    .delete(req.params.id)
    .then(() => {
      logRequest(
        req,
        `Deleted transaction with ID: ${req.params.id}`,
        "DELETE"
      );
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
