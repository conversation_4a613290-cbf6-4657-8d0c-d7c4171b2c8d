﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const communicationEmailsService = require("./communication-email.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.post("/sendTestEmails/:id", sendTestEmails);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  communicationEmailsService
    .create(req.body)
    .then((email) => {
      logRequest(
        req,
        `Created a new email template: ${email?.title} - ${email?.subject}`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}
function sendTestEmails(req, res, next) {
  communicationEmailsService
    .sendTestEmails(req.params.id, req.body)
    .then(() => {
      logRequest(
        req,
        `Sent test emails for template ID: ${req.params.id}`,
        "SEND_TEST_EMAIL"
      );
      res.json({ status: true, message: "Test emails sent successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  communicationEmailsService
    .getAll(req.query, req?.headers?.isadmin)
    .then((records) => {
      logRequest(req, "Fetched all email templates", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  communicationEmailsService
    .getById(req.params.id)
    .then((email) => {
      logRequest(
        req,
        `Fetched email template: ${email?.title} - ${email?.subject}`,
        "READ"
      );
      res.json(email);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    title: Joi.string().required(),
    subject: Joi.string().required(),
    template: Joi.string().required(),
    status: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  communicationEmailsService
    .update(req.params.id, req.body)
    .then((email) => {
      logRequest(
        req,
        `Updated email template: ${email?.title} - ${email?.subject}`,
        "UPDATE"
      );
      res.json(email);
    })
    .catch(next);
}

function _delete(req, res, next) {
  communicationEmailsService
    .delete(req.params.id)
    .then(() => {
      logRequest(
        req,
        `Deleted email template with ID: ${req.params.id}`,
        "DELETE"
      );
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
