const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    order_id: {
      type: DataTypes.STRING,
      unique: true,
      allowNull: true,
    },
    order_prefix: { type: DataTypes.STRING, allowNull: true },
    product_id: { type: DataTypes.INTEGER, allowNull: true },
    user_id: { type: DataTypes.INTEGER, allowNull: false },
    ngo_id: { type: DataTypes.INTEGER, allowNull: true },
    quantity: { type: DataTypes.INTEGER, allowNull: true },
    price_per_unit: { type: DataTypes.DECIMAL, allowNull: true },
    total_price: { type: DataTypes.DECIMAL, allowNull: true },
    order_status: { type: DataTypes.STRING, allowNull: true },
    payment_status: { type: DataTypes.STRING, allowNull: true },
    shipping_address: { type: DataTypes.TEXT, allowNull: true },
    billing_address: { type: DataTypes.TEXT, allowNull: true },
    shipping_cost: { type: DataTypes.DECIMAL, allowNull: true },
    discount_applied: { type: DataTypes.DECIMAL, allowNull: true },
    remarks: { type: DataTypes.TEXT, allowNull: true },
    type: { type: DataTypes.STRING, allowNull: true },
    paymentId: { type: DataTypes.STRING, allowNull: true },
    purpose: { type: DataTypes.STRING, allowNull: true },
    bucket_id: { type: DataTypes.INTEGER, allowNull: true },
    campaign_id: { type: DataTypes.INTEGER, allowNull: true },
    purpose: { type: DataTypes.TEXT, allowNull: true },
    purpose_donationday: { type: DataTypes.TEXT, allowNull: true },

  };

  const options = {
    defaultScope: {
      // exclude hash by default
    },
    scopes: {
      // include hash with this scope
      withHash: { attributes: {} },
    },
    hooks: {
      beforeCreate: async (order) => {
        const lastOrder = await sequelize.models.orders.findOne({
          order: [["id", "DESC"]],
        });

        let nextNumber = "00001";
        if (lastOrder && lastOrder.order_id) {
          const lastNumber = Number(lastOrder.order_id);
          nextNumber = String(lastNumber + 1).padStart(5, "0");
        }

        order.order_id = `${nextNumber}`;
      },
    },
  };

  return sequelize.define("orders", attributes, options);
}
