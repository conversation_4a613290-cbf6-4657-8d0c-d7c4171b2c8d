const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const bucketItemsService = require("./bucket-item.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  bucketItemsService
    .create(req.body)
    .then((bucketItem) => {
      logRequest(req, `Created a new bucketItem`, "CREATE");
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  bucketItemsService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, `Fetched all buckets`, "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  bucketItemsService
    .getById(req.params.id)
    .then((record) => {
      logRequest(req, `Fetched bucketItem ${record?.name}`, "READ");
      res.json(record);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    bucket_id: Joi.number().required(),
    ngo_id: Joi.number().optional(),
    campaign_id: Joi.number().optional(),
    category_id: Joi.number().optional(),
    percentage: Joi.number().optional(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  bucketItemsService
    .update(req.params.id, req.body)
    .then((record) => {
      logRequest(req, `Updated bucketItem ${record?.name}`, "UPDATE");
      res.json(record);
    })
    .catch(next);
}

function _delete(req, res, next) {
  bucketItemsService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted bucketItem with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
