const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const faqService = require("./faq.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  faqService
    .create(req.body)
    .then((faq) => {
      logRequest(
        req,
        `Created a new FAQ with question: "${faq?.question}"`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  faqService
    .getAll(req.query, req?.headers?.isadmin)
    .then((records) => {
      logRequest(req, "Fetched all FAQs", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  faqService
    .getById(req.params.id)
    .then((faq) => {
      logRequest(req, `Fetched FAQ with question: "${faq?.question}"`, "READ");
      res.json(faq);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    question: Joi.string().required(),
    answer: Joi.string().required(),
    status: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  faqService
    .update(req.params.id, req.body)
    .then((faq) => {
      logRequest(
        req,
        `Updated FAQ with question: "${faq?.question}" and answer: "${faq?.answer}"`,
        "UPDATE"
      );
      res.json(faq);
    })
    .catch(next);
}

function _delete(req, res, next) {
  faqService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted FAQ with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
