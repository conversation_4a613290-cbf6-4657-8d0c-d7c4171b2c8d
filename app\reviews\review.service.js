﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
};

db.Review.belongsTo(db.User, {
  as: "userInfo",
  through: "users",
  foreignKey: "userId",
  otherKey: "userId",
});
db.Review.belongsTo(db.Ngo, {
  as: "ngoInfo",
  through: "ngos",
  foreignKey: "ngoId",
  otherKey: "ngoId",
});

async function getAll(params) {
  const { ngoId, userId } = params;
  const where = {};
  if (ngoId) where.ngoId = ngoId;
  if (userId) where.userId = userId;
  if (params?.type) {
    where.type = params.type;
    where.typeId = params.typeId;
  }
  return await db.Review.findAll({
    where,
    order: [["id", "DESC"]],
    include: [
      {
        model: db.User,
        as: "userInfo",
        attributes: ["id", "fullname"],
      },
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name"],
      },
    ],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const record = await db.Review.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.Review.findByPk(id, {
    include: [
      {
        model: db.User,
        as: "userInfo",
        attributes: ["id", "fullname"],
      },
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name"],
      },
    ],
  });

  if (!record) throw "Record not found";
  return record;
}
