﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const journalService = require("./journal.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/:ngoId", getAll);
router.get("/sender/:senderId", getAllBySenderId);
router.post("/", updateSchema, create);
router.post("/assignAndReassignJournals", addAssignAndReassignJournals);
router.put("/:id", updateSchema, update);
router.patch("/:id", updateReadFlag);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  journalService
    .create(req.body)
    .then(() => {
      logRequest(
        req,
        `Created journal - Description: ${req.body.description}, NGO ID: ${req.body.ngo_id}, Sender ID: ${req.body.sender_id}`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function addAssignAndReassignJournals(req, res, next) {
  const { senderId, assigneeId } = req.query;
  const selectedNgoIds = req.body;

  journalService
    .addAssignAndReassignJournals(selectedNgoIds, senderId, assigneeId)
    .then(() => {
      logRequest(
        req,
        `Assigned/Reassigned journals - Sender ID: ${senderId}, Assignee ID: ${assigneeId}, NGO IDs: ${selectedNgoIds}`,
        "ASSIGN"
      );
      res.json({ status: true, message: "Records created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  const ngoId = req.params.ngoId;

  journalService
    .getAll(ngoId)
    .then((records) => {
      logRequest(req, `Fetched all journals for NGO ID: ${ngoId}`, "READ");
      res.json(records);
    })
    .catch(next);
}
function getAllBySenderId(req, res, next) {
  const senderId = req.params.senderId;
  journalService
    .getAllBySenderId(senderId)
    .then((records) => {
      logRequest(req, `Fetched all journals by Sender ID: ${senderId}`, "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  journalService
    .getById(req.params.id)
    .then((user) => res.json(user))
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    description: Joi.string().required(),
    ngo_id: Joi.number().required(),
    sender_id: Joi.number().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  journalService
    .update(req.params.id, req.body)
    .then((record) => {
      logRequest(
        req,
        `Updated journal ID: ${req.params.id} - Description: ${req.body.description}, NGO ID: ${req.body.ngo_id}, Sender ID: ${req.body.sender_id}`,
        "UPDATE"
      );
      res.json(record);
    })
    .catch(next);
}
function updateReadFlag(req, res, next) {
  journalService
    .update(req.params.id, req.body)
    .then((record) => {
      logRequest(
        req,
        `Updated read flag for journal ID: ${req.params.id}`,
        "UPDATE"
      );
      res.json(record);
    })
    .catch(next);
}

function _delete(req, res, next) {
  journalService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted journal with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
