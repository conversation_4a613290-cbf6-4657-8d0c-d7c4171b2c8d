const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const userSkillService = require("./user-skill.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);
router.delete("/user/:user_id", deleteByUserId);

module.exports = router;

function create(req, res, next) {
  userSkillService
    .create(req.body)
    .then(() => {
      logRequest(
        req,
        `Created a new user skill for user_id: ${req.body.user_id}`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  userSkillService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all user skills", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  userSkillService
    .getById(req.params.id)
    .then((userSkill) => {
      logRequest(req, `Fetched user skill with ID: ${req.params.id}`, "READ");
      res.json(userSkill);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    user_id: Joi.string().required(),
    skill_id: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  userSkillService
    .update(req.params.id, req.body)
    .then((userSkill) => {
      logRequest(
        req,
        `Updated user skill with ID: ${req.params.id} for user_id: ${req.body.user_id}`,
        "UPDATE"
      );
      res.json(userSkill);
    })
    .catch(next);
}

function _delete(req, res, next) {
  userSkillService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted user skill with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}

function deleteByUserId(req, res, next) {
  userSkillService
    .deleteByUserId(req.params.user_id)
    .then(() => {
      logRequest(
        req,
        `Deleted all user skills for user_id: ${req.params.user_id}`,
        "DELETE"
      );
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
