﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const { format } = require("date-fns");
const { Op, Sequelize } = require("sequelize");

const documentMasterService = require("../document-master/document-master.service");
const documentuserService = require("../document-users/document-user.service");

module.exports = {
  getAll,
  getAllNgos,
  getById,
  getNGOs,
  create,
  update,
  patch,
  delete: _delete,
  getByAssigneeId,
  searchByDarpanId,
  storeByAssineeIds,
  getNGOUsersByStatusParam,
  patchFullInfoByDarpanId,
  getNGOStats,
  getFullNGOStats,
  getNgoBySlugAndPhone,
  patchFullInfoByName,
  getFeaturedNGOs,
  markAsFeatured,
  getNgosByFilters,
  getFilteredNGOs,
  deleteMultipleNgos,
  inactiveNgo,
  applyNgoFilters,
};

async function getAll(
  page = 1,
  limit = 50,
  status = "All",
  assignee_id = null,
  params
) {
  const { source, state, search, isFilterOption } = params;

  const offset = (page - 1) * limit;
  let whereClause = {};

  if (status !== "All") {
    whereClause.ngo_status = status;
  }
  if (source) {
    const sourcesArray = source.split(",");
    whereClause.source = { [Op.in]: sourcesArray };
  }
  if (state) {
    whereClause.state = state;
  }

  if (search) {
    whereClause.name = { [Op.like]: `%${search}%` };
  }

  if (assignee_id) {
    whereClause.assignee_id = assignee_id;
  }

  if (isFilterOption === "yes") {
    const { count, rows } = await db.Ngo.findAndCountAll({
      where: whereClause,
      attributes: ["id", "name"],
      limit,
      offset,
      order: [["id", "DESC"]],
    });

    return {
      ngos: rows,
      totalCount: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
    };
  }

  const { count, rows } = await db.Ngo.findAndCountAll({
    where: whereClause,
    limit: limit,
    offset: offset,
    order: [["id", "DESC"]],
  });

  const statusCounts = await db.Ngo.findAll({
    attributes: [
      "ngo_status",
      [Sequelize.fn("COUNT", Sequelize.col("ngo_status")), "count"],
    ],
    where: assignee_id ? { assignee_id } : {},
    group: ["ngo_status"],
  });

  const statusCountsMap = statusCounts.reduce((acc, item) => {
    acc[item.ngo_status] = parseInt(item.dataValues.count, 10);
    return acc;
  }, {});

  statusCountsMap["All"] = await db.Ngo.count({
    where: assignee_id ? { assignee_id } : {},
  });

  const updatedNgoRecords = await Promise.all(
    rows.map(async (record) => {
      const ngoImageNames = await db.NgoImage.findAll({
        where: { ngo_id: record.id },
        attributes: ["fileName"],
      });

      const imageFileNames = ngoImageNames.map((image) => image.fileName);

      const documents = await documentMasterService.getAllForNgoSearch({
        type: "NGO",
        documentType: record.ngo_type,
      });

      const extractedDocumentdata = documents.map(
        ({ dataValues }) => dataValues
      );

      const extractedDocumentIds = extractedDocumentdata.map((doc) => doc.id);

      // const mandatoryDocuments = extractedDocumentdata.filter(
      //   (document) => document.mandatory === "yes"
      // );
      const themeImpactAreas = await db.ThemeImpactArea.findAll({
        where: {
          ngo_id: record.id,
        },
      });
      const docsSubmitted = await documentuserService.getAll({
        ngo_id: record.id,
      });
      const extractedSubmittedDocumentdata = docsSubmitted.map(
        ({ dataValues }) => dataValues
      );

      const validSubmittedDocuments = extractedSubmittedDocumentdata.filter(
        (doc) => extractedDocumentIds.includes(doc.documentId)
      );

      const submittedDocIds = validSubmittedDocuments.map(
        (doc) => doc.documentId
      );

      const financial = {
        years: "FY 23-24",
        total_revenue: 798,
        total_expenses: 10,
        income_exp: [
          {
            year: "20-21",
            data: 2.2,
          },
          {
            year: "21-22",
            data: 2.7,
          },
          {
            year: "22-23",
            data: 4.3,
          },
        ],
      };

      record.dataValues.financial = financial;
      record.dataValues.review_count = 10;
      record.dataValues.months_hosting = 10;
      record.dataValues.images = imageFileNames;
      record.dataValues.uploadedDocs = submittedDocIds.length;
      record.dataValues.totalMandatoryDocs = extractedDocumentdata.length;
      record.dataValues.themeImpactAreas = themeImpactAreas;
      record.dataValues.cleanedCategories = await getCategoriesForNGO(
        record.id
      );
      return record;
    })
  );

  return {
    ngos: updatedNgoRecords,
    totalCount: count,
    totalPages: Math.ceil(count / limit),
    currentPage: page,
    statusCounts: statusCountsMap,
  };
}

async function getAllNgos() {
  return await db.Ngo.findAll();
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // validate
  params.slug = utils.generateSlug(params.name);
  if (await db.Ngo.findOne({ where: { email: params.email } })) {
    throw 'Record "' + params.email + '" is already taken';
    return;
  }

  const record = await db.Ngo.create(params);
  return record;
}

async function update(id, params, type = null) {
  const record = await getSingleRecord(id);
  // validate
  if (!type) {
    params.slug = utils.generateSlug(params.name);
    // const recordChanged = params.slug && record.slug !== params.slug;
    // if (
    //   recordChanged &&
    //   (await db.Ngo.findOne({ where: { slug: params.slug } }))
    // ) {
    //   throw 'Record "' + params.name + '" is already taken';
    // }
  }

  const recordChanged =
    params.darpan_id && record.darpan_id !== params.darpan_id;
  if (
    recordChanged &&
    (await db.Ngo.findOne({
      where: { darpan_id: params.darpan_id },
    }))
  ) {
    throw 'Darpan Id "' + params.darpan_id + '" is already taken';
  }
  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function patch(id, params) {
  const record = await getSingleRecord(id);
  Object.assign(record, params);
  await record.save();

  return record.get();
}
async function inactiveNgo(ngoId) {
  const staffMembers = await db.PortalUser.findAll({
    where: { ngo_id: ngoId },
  });
  const campaignRecords = await db.Campaign.findAll({
    where: { ngo_id: ngoId },
  });
  const productRecords = await db.Product.findAll({
    where: { ngo_id: ngoId },
  });

  const updatePortalUsers = staffMembers?.map((user) =>
    user.update({ status: "Inactive" })
  );
  const updateCampaigns = campaignRecords?.map((campaign) =>
    campaign.update({ status: "Inactive" })
  );
  const updateProducts = productRecords.map((product) =>
    product.update({ status: "Inactive" })
  );

  await Promise.all([
    ...updatePortalUsers,
    ...updateCampaigns,
    ...updateProducts,
  ]);

  return { status: true, message: "Status updated to Inactive" };
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

// async function getSingleRecord(id) {
//   const record = await db.Ngo.findByPk(id);
//   if (!record) throw "Record not found";
//   return record;
// }

async function getSingleRecord(id) {
  const record = await db.Ngo.findByPk(id);
  if (!record) throw new Error("Record not found");

  const ngoImageNames = await db.NgoImage.findAll({
    where: { ngo_id: id },
    attributes: ["fileName"],
  });
  const imageFileNames = ngoImageNames.map((image) => image.fileName);
  const themeImpactAreas = await db.ThemeImpactArea.findAll({
    where: {
      ngo_id: id,
    },
  });
  const reviewList = await db.Review.findAll({
    where: {
      ngoId: id,
    },
  });
  const financial = {
    years: "FY 23-24",
    total_revenue: 798,
    total_expenses: 10,
    income_exp: [
      {
        year: "20-21",
        data: 2.2,
      },
      {
        year: "21-22",
        data: 2.7,
      },
      {
        year: "22-23",
        data: 4.3,
      },
    ],
  };

  const additionalFields = {
    review_count: 10,
    months_hosting: 10,
    images: imageFileNames,
  };
  record.dataValues.financial = financial;
  record.dataValues.review_count = reviewList.length;
  record.dataValues.months_hosting = 10;
  record.dataValues.images = imageFileNames;
  record.dataValues.themeImpactAreas = themeImpactAreas;
  record.dataValues.reviews = reviewList;
  record.dataValues.cleanedCategories = await getCategoriesForNGO(id);
  return record;
  //   return { ...record, financial, ...additionalFields };
}

async function getNGOUsersByStatusParam(statusParam, page = 1, limit = 10) {
  let where = {};
  if (statusParam !== "All") {
    where.ngo_status = statusParam;
  }
  const offset = (page - 1) * limit;

  const { count, rows } = await db.Ngo.findAndCountAll({
    where: where,
    limit: limit,
    offset: offset,
    order: [["id", "DESC"]],
  });

  const ngosWithCategories = await Promise.all(
    rows.map(async (ngo) => {
      const ngoCategories = await db.NgoCategory.findAll({
        where: { ngo_id: ngo.id },
        attributes: ["id", "category_id", "subCategories"],
      });

      const cleanedCategories = await Promise.all(
        ngoCategories.map(async (ngoCategory) => {
          const category = await db.Category.findOne({
            where: { id: ngoCategory.category_id },
            attributes: ["id", "name"],
          });

          return {
            id: ngoCategory.id,
            category_id: ngoCategory.category_id,
            ngo_id: ngoCategory.ngo_id,
            subCategories: ngoCategory.subCategories,
            createdAt: ngoCategory.createdAt,
            updatedAt: ngoCategory.updatedAt,
            categoryName: category ? category.name : null,
          };
        })
      );

      return {
        ...ngo.toJSON(),
        cleanedCategories,
      };
    })
  );

  return {
    ngos: ngosWithCategories,
    totalCount: count,
    totalPages: Math.ceil(count / limit),
    currentPage: page,
  };
}

async function storeByAssineeIds(params, admin_id) {
  const { ngoIdArray } = params;

  const ngoUpdates = ngoIdArray.map(async (ngo_id) => {
    try {
      const currentTime = format(new Date(), "yyyy-MM-dd HH:mm:ss");

      const ngoRecord = await getById(ngo_id);
      const updateData = {
        assignee_id: admin_id,
      };

      if (ngoRecord.assigned_on) {
        updateData.reassigned_on = currentTime;
      } else {
        updateData.assigned_on = currentTime;
        if (ngoRecord.ngo_status == "New") {
          updateData.ngo_status = "Pending";
        }
      }

      await update(ngo_id, updateData, "assignee");
    } catch (error) {
      console.error(`Failed to assign admin ID to NGO ${ngo_id}:`, error);
    }
  });

  await Promise.all(ngoUpdates);
}

async function markAsFeatured(ngoId, featureMode) {
  if (!ngoId) {
    throw new Error("ngo ID is required.");
  }

  const isFeatured = featureMode === "add";
  try {
    await update(ngoId, { isfeatured: isFeatured ? "yes" : "no" }, "featured");
  } catch (error) {
    throw new Error(`Failed to mark ngo as featured: ${error.message}`);
  }
}

async function getByAssigneeId(assigneeId, status) {
  let result;
  if (status !== undefined) {
    result = await db.Ngo.findAll({
      where: { assignee_id: assigneeId, ngo_status: status },
    });
  } else {
    result = await db.Ngo.findAll({
      where: { assignee_id: assigneeId },
    });
  }

  if (!result || result.length === 0)
    throw "No NGO users found for this assignee id and status";

  return result;
}
async function getFeaturedNGOs() {
  const result = await db.Ngo.findAll({
    where: { isfeatured: "yes" },
  });

  if (!result || result.length === 0) throw "No featured ngo found ";

  return result;
}

async function getNGOs(params) {
  const { search, status, portalUserId } = params;
  const whereClause = {};

  if (search) {
    whereClause.name = { [Sequelize.Op.like]: `%${search.trim()}%` }; // Trim whitespace
  }
  if (status) {
    whereClause.ngo_status = status;
  }
  if (portalUserId) {
    whereClause.assignee_id = portalUserId;
  }

  // Fetch NGOs from DB
  const ngos = await db.Ngo.findAll({ where: whereClause });

  // Process each NGO in parallel using Promise.all
  await Promise.all(
    ngos.map(async (record) => {
      const [documents, docsSubmitted] = await Promise.all([
        documentMasterService.getAllForNgoSearch({
          type: "NGO",
          documentType: record.ngo_type,
        }),
        documentuserService.getAll({ ngo_id: record.id }),
      ]);

      // Extract document data
      const extractedDocumentData = documents.map(
        ({ dataValues }) => dataValues
      );
      // const mandatoryDocuments = extractedDocumentData.filter(
      //   (document) => document.mandatory === "yes"
      // );

      // Extract submitted document data
      const extractedSubmittedDocumentData = docsSubmitted.map(
        ({ dataValues }) => dataValues
      );
      const submittedDocIds = extractedSubmittedDocumentData.map(
        (doc) => doc.documentId
      );

      // Append calculated values
      record.dataValues.uploadedDocs = submittedDocIds.length;
      record.dataValues.totalMandatoryDocs = extractedDocumentData.length;
    })
  );

  return ngos;
}

async function patchFullInfoByDarpanId(darpanId, params) {
  if (!darpanId) {
    return res.status(400).json({ error: "Missing darpanId in request body" });
  }
  const record = await db.Ngo.findOne({ where: { darpan_id: darpanId } });

  if (!record) {
    throw new Error(`NGO with darpan_id ${darpanId} not found.`);
  }
  Object.assign(record, params);

  await record.save();

  return record.get(); // Return the updated record
}
async function patchFullInfoByName(ngoname, params) {
  if (!ngoname) {
    return res.status(400).json({ error: "Missing name in request body" });
  }
  const record = await db.Ngo.findOne({
    where: { name: ngoname },
  });

  if (!record) {
    throw new Error(`NGO with name ${ngoname} not found.`);
  }
  Object.assign(record, params);

  await record.save();

  return record.get(); // Return the updated record
}
async function getNGOStats(assigneeID) {
  // Counts based on NGO status
  const pendingCount = await db.Ngo.count({
    where: { ngo_status: "Pending", assignee_id: assigneeID },
  });
  const inReviewCount = await db.Ngo.count({
    where: { ngo_status: "In Review", assignee_id: assigneeID },
  });

  // Counts based on assignment status

  const inReviewNgos = await db.Ngo.count({
    where: { ngo_status: "In Review", assignee_id: assigneeID },
  });
  const verifiedNgos = await db.Ngo.count({
    where: { ngo_status: "Verified", assignee_id: assigneeID },
  });
  const totalAssignedNgos = await db.Ngo.count({
    where: { assignee_id: assigneeID },
  });

  return {
    alertMessages: {
      pendingCount,
      inReviewCount,
    },
    ngoStats: {
      pendingCount,
      inReviewNgos,
      verifiedNgos,
      totalAssignedNgos,
    },
  };
}
async function getFullNGOStats() {
  // Counts based on NGO status
  const pendingCount = await db.Ngo.count({
    where: { ngo_status: "Pending" },
  });
  const inReviewCount = await db.Ngo.count({
    where: { ngo_status: "In Review" },
  });

  // Counts based on assignment status

  const inReviewNgos = await db.Ngo.count({
    where: { ngo_status: "In Review" },
  });
  const verifiedNgos = await db.Ngo.count({
    where: { ngo_status: "Verified" },
  });
  const totalAssignedNgos = await db.Ngo.count();

  const inactiveNgos = await db.PortalUser.count({
    where: { status: "Inactive" },
  });

  return {
    alertMessage: {
      inReviewNgoCount: inReviewCount,
    },
    ngoStats: {
      totalNgos: totalAssignedNgos,
      inReviewNgos: inReviewNgos,
      pendingNgos: pendingCount,
      verifiedNgos: verifiedNgos,
      inactiveNgos: inactiveNgos,
    },
  };
}

async function getNgosByFilters(filters) {
  const {
    latitude,
    longitude,
    radius = 10,
    ngos, // [object] with id
    causes, // [object] with id
    certificates, // [string]
    ngoType,
    search,
    impactLive,
    revenue,
    expense,
    activeCampaigns,
    modeOfSupport,
    page = 1,
    limit = 10,
  } = filters;

  const offset = (page - 1) * limit;

  const whereClause = { ngo_status: "Verified" };

  // NGO ID filter - short-circuit and return immediately
  if (Array.isArray(ngos) && ngos.length > 0) {
    const ngoIds = ngos.map((ngo) => ngo.id);

    const ngoList = await db.Ngo.findAll({
      where: {
        id: { [Op.in]: ngoIds },
        ngo_status: "Verified",
      },
      attributes: [
        "id",
        "name",
        "email",
        "darpan_id",
        "type_of_ngo",
        "point_of_contact_name",
        "point_of_contact_mobile_number",
        "registered_address",
        "current_address",
        "date_of_establishment",
        "latitude",
        "longitude",
        "pincode",
        "state",
        "place_name",
        "claimed_ngo",
        "vision",
        "mission",
        "grade",
        "rating",
        "nr_city_name",
        "ngo_type",
        "country",
        "source",
        "ngo_status",
        "isfeatured",
        "createdAt",
        "updatedAt",
      ],
    });

    const paginated = ngoList.slice(offset, offset + limit);
    const ngosWithCategories = await appendCategoriesToNgos(paginated);
    return {
      data: ngosWithCategories,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(ngoList.length / limit),
        totalCount: ngoList.length,
      },
    };
  }

  // Grade-based filter
  if (Array.isArray(ngoType) && ngoType.length > 0) {
    const gradeValues = ngoType
      .map((type) => {
        if (type.includes("Gold")) return "Gold";
        if (type.includes("Silver")) return "Silver";
        if (type.includes("Bronze")) return "Bronze";
        return null;
      })
      .filter(Boolean);
    whereClause.grade = { [Op.in]: gradeValues };
  }

  // Certificate filters
  let certConditions = [];
  if (Array.isArray(certificates) && certificates.length > 0) {
    certConditions = certificates.map((cert) =>
      Sequelize.literal(
        `JSON_UNQUOTE(JSON_EXTRACT(CAST(registration_details AS JSON), '$.${cert}')) IS NOT NULL`
      )
    );
  }

  // Cause/category filter
  let causeFilteredNgoIds = null;
  if (Array.isArray(causes) && causes.length > 0) {
    const categoryIds = causes.map((cause) => cause.id);
    const ngoCategories = await db.NgoCategory.findAll({
      where: { category_id: categoryIds },
    });
    causeFilteredNgoIds = ngoCategories.map((item) => item.ngo_id);
  }

  // Search filter
  if (search) {
    whereClause.name = { [Op.like]: `%${search}%` };
  }

  // Main DB query with pagination
  let ngosList = await db.Ngo.findAll({
    where: {
      ...whereClause,
      ...(causeFilteredNgoIds && {
        id: { [Op.in]: causeFilteredNgoIds },
      }),
      ...(certConditions.length > 0 && {
        [Op.and]: certConditions,
      }),
    },
    order: [["id", "DESC"]],
    attributes: [
      "id",
      "name",
      "email",
      "darpan_id",
      "type_of_ngo",
      "point_of_contact_name",
      "point_of_contact_mobile_number",
      "registered_address",
      "current_address",
      "date_of_establishment",
      "latitude",
      "longitude",
      "pincode",
      "state",
      "place_name",
      "claimed_ngo",
      "vision",
      "mission",
      "grade",
      "rating",
      "nr_city_name",
      "ngo_type",
      "country",
      "source",
      "ngo_status",
      "isfeatured",
      "createdAt",
      "updatedAt",
    ],
  });

  // In-memory geolocation filter (after pagination fetch)
  if (latitude && longitude) {
    const EARTH_RADIUS = 6371;
    const toRad = (value) => (value * Math.PI) / 180;

    ngosList = ngosList.filter((ngo) => {
      const ngoLat = parseFloat(ngo.latitude);
      const ngoLng = parseFloat(ngo.longitude);
      if (isNaN(ngoLat) || isNaN(ngoLng)) return false;

      const dLat = toRad(ngoLat - latitude);
      const dLng = toRad(ngoLng - longitude);
      const lat1 = toRad(latitude);
      const lat2 = toRad(ngoLat);

      const a =
        Math.sin(dLat / 2) ** 2 +
        Math.sin(dLng / 2) ** 2 * Math.cos(lat1) * Math.cos(lat2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      const distance = EARTH_RADIUS * c;

      return distance <= radius;
    });

    ngosList = ngosList.slice(offset, offset + limit);
  }
  const totalCount = ngosList.length;
  const paginated = ngosList.slice(offset, offset + limit);
  const ngosWithCategories = await appendCategoriesToNgos(paginated);

  if (!ngosList.length) {
    return {
      data: [],
      pagination: {
        currentPage: page,
        totalPages: 0,
        totalCount: 0,
      },
    };
  }

  return {
    data: ngosWithCategories,
    pagination: {
      currentPage: page,
      totalPages: Math.ceil(totalCount / limit),
      totalCount,
    },
  };
}

// Helper to append category data
async function appendCategoriesToNgos(ngoList) {
  return await Promise.all(
    ngoList.map(async (ngo) => {
      const cleanedCategories = await getCategoriesForNGO(ngo.id);
      return {
        ...ngo.toJSON(),
        cleanedCategories,
      };
    })
  );
}

function degreesToRadians(degrees) {
  return (degrees * Math.PI) / 180;
}

async function getNgoBySlugAndPhone(slug, phone_number) {
  const whereClause = {};
  // whereClause.ngo_status = "New";
  whereClause.source = {
    [Op.or]: ["Internet", "Self", "Matched"],
  };

  whereClause.claimed_ngo = {
    [Op.or]: ["no", null, ""],
  };

  if (slug) {
    whereClause.slug = slug;
  }

  const result = await db.Ngo.findAll({
    attributes: [
      "id",
      "name",
      "email",
      "registered_address",
      "current_address",
    ],
    where: whereClause,
  });
  if (!result) {
    throw new Error("NGO not found for the provided slug and phone number.");
  }
  return result;
}

async function searchByDarpanId(darpanId) {
  const whereClause = {};
  if (darpanId) {
    whereClause.darpan_id = darpanId;
  }

  // whereClause.source = {
  //   [Op.or]: ["Internet", "Self", "Matched"],
  // };

  // Fetch NGO details
  const ngoResult = await db.Ngo.findAll({
    attributes: [
      "id",
      "name",
      "email",
      "registered_address",
      "current_address",
    ],
    where: whereClause,
  });

  const ngoRecord = await db.Ngo.findOne({
    where: { darpan_id: darpanId },
  });

  let portalUserByEmail;
  if (ngoRecord) {
    portalUserByEmail = await db.PortalUser.findOne({
      where: { email: ngoRecord?.email },
      attributes: ["id", "fullname", "email", "status"],
    });
  }
  if (ngoRecord?.source == "Internet" && portalUserByEmail) {
    throw `We are reviewing your NGO profile and will get back to you soon.`;
  }

  if (!ngoRecord) {
    return {
      message: "No associated NGO record found.",
      ngoDetails: ngoResult,
    };
  }

  const portalUser = await db.PortalUser.findOne({
    where: { ngo_id: ngoRecord?.id },
    attributes: ["id", "fullname", "email", "status"],
  });

  if (!portalUser) {
    return {
      message: "No associated portal user found. Email sent to inactive user.",
      ngoDetails: ngoResult,
    };
  }

  return {
    ngoDetails: ngoResult,
    portalUserDetails: portalUser,
  };
}

async function getFilteredNGOs(filters) {
  const {
    state,
    source,
    ngo_status,
    assignee_id,
    page,
    limit,
    searchTerm,
    portalUserId,
    status,
  } = filters;
  const whereClause = {};

  if (portalUserId) {
    whereClause.assignee_id = portalUserId;
  }
  if (state) {
    whereClause.state = state;
  }
  if (status) {
    whereClause.ngo_status = status;
  }
  if (source) {
    const sourcesArray = source.split(",");
    whereClause.source = { [Op.in]: sourcesArray };
  }
  if (ngo_status) {
    whereClause.ngo_status = ngo_status;
  }
  if (assignee_id) {
    whereClause.assignee_id = assignee_id;
  }

  if (searchTerm) {
    whereClause.name = { [Op.like]: `%${searchTerm}%` };
  }

  const queryOptions = {
    where: whereClause,
    order: [["id", "DESC"]],
  };

  if (page && limit) {
    const offset = (parseInt(page) - 1) * parseInt(limit);
    queryOptions.limit = parseFloat(limit);
    queryOptions.offset = offset;
  }

  try {
    const { count, rows } = await db.Ngo.findAndCountAll(queryOptions);

    const updatedNgoRecords = await Promise.all(
      rows.map(async (record) => {
        const documents = await documentMasterService.getAllForNgoSearch({
          type: "NGO",
          documentType: record.ngo_type,
        });

        const extractedDocumentdata = documents.map(
          ({ dataValues }) => dataValues
        );

        const extractedDocumentIds = extractedDocumentdata.map((doc) => doc.id);

        const docsSubmitted = await documentuserService.getAll({
          ngo_id: record.id,
        });
        const extractedSubmittedDocumentdata = docsSubmitted.map(
          ({ dataValues }) => dataValues
        );

        const validSubmittedDocuments = extractedSubmittedDocumentdata.filter(
          (doc) => extractedDocumentIds.includes(doc.documentId)
        );

        const submittedDocIds = validSubmittedDocuments.map(
          (doc) => doc.documentId
        );

        let assigneeInfo = null;

        if (record?.assignee_id) {
          assigneeInfo = await db.PortalUser.findOne({
            where: { id: record.assignee_id },
          });
        }

        record.dataValues.assigneeName = assigneeInfo?.fullname;
        record.dataValues.uploadedDocs = submittedDocIds.length;
        record.dataValues.totalMandatoryDocs = extractedDocumentdata.length;

        record.dataValues.documents_uploaded = `${submittedDocIds.length}/${extractedDocumentdata.length}`;

        record.dataValues.createdAt = utils.formatDate(
          record.dataValues.createdAt
        );
        record.dataValues.updatedAt = utils.formatDate(
          record.dataValues.updatedAt
        );
        record.dataValues.assigned_on = utils.formatDate(
          record.dataValues.assigned_on
        );

        return record;
      })
    );

    return {
      ngos: updatedNgoRecords,
      totalCount: count,
      totalPages: limit ? Math.ceil(count / limit) : 1,
      currentPage: page || 1,
    };
  } catch (error) {
    console.error("Error fetching filtered NGOs:", error.message);
    throw new Error("Failed to fetch filtered NGOs.");
  }
}
async function applyNgoFilters(filters) {
  const {
    state,
    source,
    ngo_status,
    assignee_id,
    page,
    limit,
    searchTerm,
    portalUserId,
    status,
  } = filters;
  const whereClause = {};

  if (portalUserId) {
    whereClause.assignee_id = portalUserId;
  }
  if (state) {
    whereClause.state = state;
  }
  if (status) {
    whereClause.ngo_status = status;
  }
  if (source) {
    const sourcesArray = source.split(",");
    whereClause.source = { [Op.in]: sourcesArray };
  }
  if (ngo_status) {
    whereClause.ngo_status = ngo_status;
  }
  if (assignee_id) {
    whereClause.assignee_id = assignee_id;
  }

  if (searchTerm) {
    whereClause.name = { [Op.like]: `%${searchTerm}%` };
  }

  const queryOptions = {
    where: whereClause,
    order: [["id", "DESC"]],
  };

  if (page && limit) {
    const offset = (parseInt(page) - 1) * parseInt(limit);
    queryOptions.limit = parseFloat(limit);
    queryOptions.offset = offset;
  }

  try {
    const { count, rows } = await db.Ngo.findAndCountAll(queryOptions);

    const updatedNgoRecords = await Promise.all(
      rows.map(async (record) => {
        const documents = await documentMasterService.getAllForNgoSearch({
          type: "NGO",
          documentType: record.ngo_type,
        });

        const extractedDocumentdata = documents.map(
          ({ dataValues }) => dataValues
        );

        const extractedDocumentIds = extractedDocumentdata.map((doc) => doc.id);

        const docsSubmitted = await documentuserService.getAll({
          ngo_id: record.id,
        });
        const extractedSubmittedDocumentdata = docsSubmitted.map(
          ({ dataValues }) => dataValues
        );

        const validSubmittedDocuments = extractedSubmittedDocumentdata.filter(
          (doc) => extractedDocumentIds.includes(doc.documentId)
        );

        const submittedDocIds = validSubmittedDocuments.map(
          (doc) => doc.documentId
        );

        let assigneeInfo = null;

        if (record?.assignee_id) {
          assigneeInfo = await db.PortalUser.findOne({
            where: { id: record.assignee_id },
          });
        }

        record.dataValues.assigneeName = assigneeInfo?.fullname;
        record.dataValues.uploadedDocs = submittedDocIds.length;
        record.dataValues.totalMandatoryDocs = extractedDocumentdata.length;

        record.dataValues.documents_uploaded = `${submittedDocIds.length}/${extractedDocumentdata.length}`;

        record.dataValues.createdAt = utils.formatDate(
          record.dataValues.createdAt
        );
        record.dataValues.updatedAt = utils.formatDate(
          record.dataValues.updatedAt
        );
        record.dataValues.assigned_on = utils.formatDate(
          record.dataValues.assigned_on
        );

        return record;
      })
    );

    return {
      ngos: updatedNgoRecords,
      totalCount: count,
      totalPages: limit ? Math.ceil(count / limit) : 1,
      currentPage: page || 1,
    };
  } catch (error) {
    console.error("Error fetching filtered NGOs:", error.message);
    throw new Error("Failed to fetch filtered NGOs.");
  }
}

async function deleteMultipleNgos(params) {
  const { ngoIdArray } = params;

  const deletePromises = ngoIdArray.map(async (ngo_id) => {
    try {
      const ngoRecord = await getById(ngo_id);
      if (ngoRecord) {
        await ngoRecord.destroy();
      }
    } catch (error) {
      console.error(`Failed to delete NGO ${ngo_id}:`, error);
    }
  });

  await Promise.all(deletePromises);
}

async function getCategoriesForNGO(ngoId) {
  const ngoCategories = await db.NgoCategory.findAll({
    where: { ngo_id: ngoId },
    attributes: ["id", "category_id", "subCategories"],
  });
  const cleanedCategories = await Promise.all(
    ngoCategories.map(async (ngoCategory) => {
      const category = await db.Category.findOne({
        where: { id: ngoCategory.category_id },
        attributes: ["id", "name"],
      });

      return {
        subCategories: ngoCategory.subCategories,
        categoryName: category ? category.name : null,
      };
    })
  );
  return cleanedCategories;
}
