﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
};

async function getAll(params) {
  const whereClause = {};

  if (params?.userId) {
    whereClause.user_id = params.userId;
  }
  if (params?.deviceToken) {
    whereClause.device_token = params.deviceToken;
  }

  const UserTokens = await db.UserToken.findAll({
    where: whereClause,
    order: [["id", "DESC"]],
  });

  return UserTokens;
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const record = await db.UserToken.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
  return record;
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.UserToken.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
