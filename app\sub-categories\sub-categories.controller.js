﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const subcategoriesService = require("./sub-category.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  subcategoriesService
    .create(req.body)
    .then((subcategory) => {
      logRequest(
        req,
        `Created subcategory ${subcategory?.name} under category ID: ${subcategory?.category_id}`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  subcategoriesService
    .getAll(req.query, req?.headers?.isadmin)
    .then((records) => {
      logRequest(req, "Fetched all subcategories", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  subcategoriesService
    .getById(req.params.id)
    .then((subcategory) => {
      logRequest(
        req,
        `Fetched subcategory ${subcategory?.name} under category ID: ${subcategory?.category_id}`,
        "READ"
      );
      res.json(subcategory);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    description: Joi.string().allow("", null),
    category_id: Joi.number().required(),
    status: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  subcategoriesService
    .update(req.params.id, req.body)
    .then((subcategory) => {
      logRequest(
        req,
        `Updated subcategory ${subcategory?.name} under category ID: ${subcategory?.category_id}`,
        "UPDATE"
      );
      res.json(subcategory);
    })
    .catch(next);
}

function _delete(req, res, next) {
  subcategoriesService
    .delete(req.params.id)
    .then(() => {
      logRequest(
        req,
        `Deleted subcategory with ID: ${req.params.id}`,
        "DELETE"
      );
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
