const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    fullname: { type: DataTypes.STRING, allowNull: true },
    gender: { type: DataTypes.STRING, allowNull: true },
    mobile_number: { type: DataTypes.STRING, allowNull: true },
    monthlyDonationGoal: { type: DataTypes.INTEGER, allowNull: true },
    place_name: { type: DataTypes.STRING, allowNull: true },
    latitude: { type: DataTypes.STRING, allowNull: true },
    longitude: { type: DataTypes.STRING, allowNull: true },
    pincode: { type: DataTypes.STRING, allowNull: true },
    state: { type: DataTypes.STRING, allowNull: true },
    age: { type: DataTypes.INTEGER, allowNull: true },
    about: { type: DataTypes.TEXT, allowNull: true },
    skills: { type: DataTypes.TEXT, allowNull: true },
    token: { type: DataTypes.STRING, allowNull: true },
    otp: { type: DataTypes.STRING, allowNull: true },
    primary_motivation: { type: DataTypes.STRING, allowNull: true },
    events_participation: { type: DataTypes.STRING, allowNull: true },
    status: { type: DataTypes.STRING, allowNull: true },

    dob: { type: DataTypes.DATE, allowNull: true },
    email: { type: DataTypes.STRING, allowNull: true },
    password: { type: DataTypes.STRING, allowNull: true },
    pan: { type: DataTypes.STRING, allowNull: true },
    occupation: { type: DataTypes.STRING, allowNull: true },
    citizenship: { type: DataTypes.STRING, allowNull: true },
    interests: { type: DataTypes.STRING, allowNull: true },
    purposes: { type: DataTypes.STRING, allowNull: true },
    city: { type: DataTypes.STRING, allowNull: true },
    address_line_1: { type: DataTypes.STRING, allowNull: true },
    address_line_2: { type: DataTypes.STRING, allowNull: true },
    socialValues: { type: DataTypes.TEXT, allowNull: true },
    loginType: { type: DataTypes.STRING, allowNull: true },
    emailotp: { type: DataTypes.STRING, allowNull: true },
    total_impact_created: { type: DataTypes.INTEGER, allowNull: true },
  };

  const options = {
    defaultScope: {
      // exclude hash by default
    },
    scopes: {
      // include hash with this scope
      withHash: { attributes: {} },
    },
  };

  return sequelize.define("users", attributes, options);
}
