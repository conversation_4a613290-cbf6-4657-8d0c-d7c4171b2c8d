﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const { default: axios } = require("axios");
const jwt = require("jsonwebtoken");
const config = require("../../config.json");
const { Op } = require("sequelize");
const moment = require("moment");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  sendVerificationOTP,
  verifyOtp,
  verifyPan,
};

async function getAll(params) {
  const TempUsers = await db.TempUser.findAll({
    order: [["id", "DESC"]],
  });

  return TempUsers;
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const record = await db.TempUser.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
  return record;
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.TempUser.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}

async function sendVerificationOTP(params) {
  const otp = utils.generateOtp();
  params.otp = otp;

  const existingUser = await db.TempUser.findOne({
    where: { mobile_number: params?.mobile_number },
  });

  if (existingUser) {
    // Update OTP for existing mobile number
    await db.TempUser.update(
      { otp: otp },
      { where: { mobile_number: params?.mobile_number } }
    );
  } else {
    // Insert new record with mobile number and OTP
    await db.TempUser.create({
      mobile_number: params?.mobile_number,
      otp: otp,
    });
  }

  const response = await generateOtp(params);

  if (response) {
    return {
      status: true,
      message: "OTP has been sent to your mobile number",
    };
  } else {
    return {
      status: false,
      message: "Something went wrong, please try again later",
    };
  }
}

async function verifyOtp(params) {
  const checkIfRecordExists = await db.TempUser.findOne({
    where: { mobile_number: params?.mobile_number, otp: params.otp },
  });
  if (!checkIfRecordExists?.id) {
    return { status: false, message: "No user found", user: null };
  } else {
    const token = jwt.sign({ sub: checkIfRecordExists.id }, config.secret, {
      expiresIn: "7d",
    });
    return { status: true, user: checkIfRecordExists, token };
  }
}

// async function verifyPan(panDetails) {
//   const data = JSON.stringify({
//     pan: panDetails.pan,
//     consent: "Y",
//     reason: "Reason for verifying PAN set by the developer",
//   });

//   const config = {
//     method: "post",
//     maxBodyLength: Infinity,
//     url: "https://dg-sandbox.setu.co/api/verify/pan",
//     headers: {
//       "x-client-id": "a0a7b991-7184-474a-be11-80fd4fc049c2",
//       "x-client-secret": "Cfk4KTbxfwS6iK7946sOGdu0nZhvR7lM",
//       "x-product-instance-id": "8586d08e-fda9-4680-bed3-d0d172166c72",
//       "Content-Type": "application/json",
//     },
//     data: data,
//   };

//   try {
//     const response = await axios.request(config);
//     if (response?.data?.verification === "failed") {
//       return { status: false, data: response.data };
//     }
//     return { status: true, data: response.data };
//   } catch (error) {
//     console.error(error);
//     return { status: false, message: "Error verifying PAN" };
//   }
// }

async function verifyPan(panDetails) {
  const user = await db.TempUser.findOne({
    where: { mobile_number: panDetails.mobile_number },
  });

  if (!user) {
    return { status: false, message: "User not found" };
  }

  const today = moment().format("YYYY-MM-DD");

  // Reset attempts for a new day
  if (user.last_attempt_date !== today) {
    user.attempt_count = 0;
    user.is_blocked = "no";
    await db.TempUser.update(
      { attempt_count: 0, is_blocked: "no" },
      {
        where: { mobile_number: panDetails.mobile_number },
      }
    );
  }

  if (user.is_blocked === "yes" && user.last_attempt_date === today) {
    return {
      status: false,
      message:
        "You have exceeded the maximum number of PAN verification attempts. Try again tomorrow.",
    };
  }
  // Pre-increment and check limit BEFORE external API call
  user.attempt_count += 1;

  user.last_attempt_date = today;

  const updateData = {
    attempt_count: user.attempt_count,
    last_attempt_date: today,
    is_blocked: user.attempt_count >= 3 ? "yes" : "no",
  };

  await db.TempUser.update(updateData, {
    where: { mobile_number: panDetails.mobile_number },
  });

  if (user.attempt_count > 3) {
    return {
      status: false,
      message:
        "You have exceeded the maximum number of PAN verification attempts. Try again tomorrow.",
    };
  }

  // Proceed with API call
  const result = await callPanVerificationApi(panDetails);

  return result;
}

// Move the original PAN API logic here
async function callPanVerificationApi(panDetails) {
  const data = JSON.stringify({
    pan: panDetails.pan,
    consent: "Y",
    reason: "Reason for verifying PAN set by the developer",
  });

  const configuration = {
    method: "post",
    maxBodyLength: Infinity,
    url: "https://dg.setu.co/api/verify/pan",
    headers: {
      "x-client-id": config.PAN_X_ID,
      "x-client-secret": config.PAN_CLIENT_SECRET,
      "x-product-instance-id": config.PAN_PRODUCT_ID,
      "Content-Type": "application/json",
    },
    data: data,
  };

  try {
    const response = await axios.request(configuration);
    if (response?.data?.verification === "failed") {
      return { status: false, data: response.data };
    }
    return { status: true, data: response.data };
  } catch (error) {
    console.error(error);
    return { status: false, message: "Error verifying PAN" };
  }
}

async function generateOtp(params) {
  const message = encodeURIComponent(
    `${params.otp} is the OTP to verify your mobile number with DoRight. Please do not share this OTP with anyone.-DORGHT`
  );
  await axios.get(
    `https://sms6.rmlconnect.net:8443/bulksms/bulksms?username=zensocio&password=y8lG-%5BD7&type=0&dlr=1&destination=91${params?.mobile_number}&source=DORGHT&message=${message}&entityid=1201173131450491942&tempid=1207173349829291055`
  );
  return true;
}
