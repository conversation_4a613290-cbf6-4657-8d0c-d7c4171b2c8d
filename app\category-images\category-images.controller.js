const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const categoryImageService = require("./category-image.service");
const { logAction } = require("../_helpers/logger");

const multer = require("multer");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/category-images";
    cb(null, __basedir + "uploads/category-images/");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

const uploadConfig = multer({
  storage: storage,
});

// routes
router.get("/:categoryId", getAll);
router.post("/", uploadConfig.single("file"), updateSchema, create);
// router.get("/:id", getById);
router.put("/:id", uploadConfig.single("file"), updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  categoryImageService
    .create(req.body)
    .then(() =>
      res.json({ status: true, message: "Record created successfully" })
    )
    .catch(next);
}

function getAll(req, res, next) {
  const categoryId = req.params.categoryId;

  categoryImageService
    .getAll(categoryId)
    .then((records) => {
     
      return res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  categoryImageService
    .getById(req.params.id)
    .then((portalUserCategory) => res.json(portalUserCategory))
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    fileName: Joi.string().required(),
    category_id: Joi.number().required(),
    description: Joi.string().optional().allow("", null),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  categoryImageService
    .update(req.params.id, req.body)
    .then((portalUserCategory) => res.json(portalUserCategory))
    .catch(next);
}

function _delete(req, res, next) {
  categoryImageService
    .deleteByCategoryId(req.params.id)
    .then(() =>
      res.json({ status: true, message: "Records deleted successfully" })
    )
    .catch(next);
}
