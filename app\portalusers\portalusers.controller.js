﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request.js");
const authorize = require("../_middleware/authorize");
const testEmail = require("../_helpers/util.js");

const portalUserService = require("./portaluser.service");
const { logAction } = require("../_helpers/logger.js");

// Common Logging Function
function logRequest(req, action, type, authenticatedId = null) {
  const userId =
    type === "AUTHENTICATE" ? authenticatedId : req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.post("/authenticate", authenticateSchema, authenticate);
router.post("/", registerSchema, create);
// router.get("/", authorize(), getAll);
router.get("/", getAll);
router.get("/getDRStaffMembers", getDRStaffMembers);
router.get("/getAssignedStaffMembers", getAssignedStaffMembers);
router.get("/getNgoStaffMembers", getByNGOId);
router.get("/role/:type", getByRoleType);
router.get("/validateEmail", validateEmail);
router.get("/sendVerificationEmail", sendVerificationEmail);
router.get("/verifyEmail", verifyEmail);
// router.get("/current", authorize(), getCurrent);
router.get("/:id", getById);
// router.put("/:id", authorize(), updateSchema, update);
router.put("/:id", updateSchema, update);
router.patch("/:id", update);
//for ngo array
// router.delete("/:id", authorize(), _delete);
router.delete("/:id", _delete);
router.post("/requestPasswordReset", requestPasswordReset);
router.post("/doPasswordReset", doPasswordReset);
router.post("/doSendEmail", doSendEmail);

module.exports = router;

function authenticateSchema(req, res, next) {
  const schema = Joi.object({
    email: Joi.string().allow(null, ""),
    password: Joi.string().allow(null, ""),
  });
  validateRequest(req, next, schema);
}

function doSendEmail() {
  testEmail.sendTestEmail("<EMAIL>");
}

function authenticate(req, res, next) {
  portalUserService
    .authenticate(req.body)
    .then((user) => {
      logRequest(
        req,
        `User authenticated: ${user?.user?.email} ${user?.user.fullname}`,
        "AUTHENTICATE",
        user?.user?.id
      );
      res.json({ status: true, result: user });
    })
    .catch(next);
}

function registerSchema(req, res, next) {
  const schema = Joi.object({
    fullname: Joi.string().allow(null, ""),
    email: Joi.string().email().allow(null, ""),
    password: Joi.string().allow(null, ""),
    darpan_id: Joi.string().allow(null, ""),
    role_id: Joi.number().integer().allow(null, ""),
    ngo_id: Joi.number().integer().allow(null, ""),
    pan: Joi.string().allow(null, ""),
    panNgoName: Joi.string().allow(null, ""),
    status: Joi.string().allow(null, ""),
    current_address: Joi.string().allow(null, ""),
    point_of_contact_mobile_number: Joi.string().allow(null, ""),
    token: Joi.string().allow(null, ""),
  });

  validateRequest(req, next, schema);
}

function create(req, res, next) {
  portalUserService
    .create(req.body)
    .then((record) => {
      logRequest(req, `Created a new user ${record?.fullname}`, "CREATE");
      res.json({
        status: true,
        message: "User created successfully",
        userId: record.id,
      });
    })
    .catch(next);
}

function getDRStaffMembers(req, res, next) {
  portalUserService
    .getDRStaffMembers(req.query)
    .then((users) => {
      logRequest(req, "Fetched DR staff members", "READ");
      res.json(users);
    })
    .catch(next);
}
function getAssignedStaffMembers(req, res, next) {
  portalUserService
    .getAssignedStaffMembers(req.query)
    .then((users) => {
      logRequest(req, "Fetched assigned staff members", "READ");
      res.json(users);
    })
    .catch(next);
}

function getByNGOId(req, res, next) {
  portalUserService
    .getByNGOId(req.query)
    .then((users) => {
      logRequest(
        req,
        `Fetched all staff members for NGO ID ${req.query.ngoId}`,
        "READ"
      );
      res.json(users);
    })
    .catch(next);
}

function getAll(req, res, next) {
  portalUserService
    .getAll(req.query)
    .then((users) => {
      logRequest(req, "Fetched all users", "READ");
      res.json(users);
    })
    .catch(next);
}

function getCurrent(req, res, next) {
  logRequest(req, "Fetched current user data", "READ");
  res.json(req.user);
}

function getById(req, res, next) {
  portalUserService
    .getById(req.params.id)
    .then((user) => {
      logRequest(req, `Fetched user with ID ${req.params.id}`, "READ");
      res.json(user);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    fullname: Joi.string().allow(null, ""),
    email: Joi.string().email().allow(null, ""),
    password: Joi.string().allow(null, ""),
    role_id: Joi.number().integer().allow(null, ""),
    ngo_id: Joi.number().integer().allow(null, ""),
    createdBy: Joi.number().integer().allow(null, ""),
    pan: Joi.string().allow(null, ""),
    panNgoName: Joi.string().allow(null, ""),
    status: Joi.string().allow(null, ""),
    current_address: Joi.string().allow(null, ""),
    point_of_contact_mobile_number: Joi.string().allow(null, ""),
    token: Joi.string().allow(null, ""),
  });

  validateRequest(req, next, schema);
}

function update(req, res, next) {
  portalUserService
    .update(req.params.id, req.body)
    .then((user) => {
      logRequest(req, `Updated user ${user?.fullname}`, "UPDATE");
      res.json(user);
    })
    .catch(next);
}

function _delete(req, res, next) {
  portalUserService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted user with ID ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}

function getByRoleType(req, res, next) {
  const roleType = req.params.type;
  portalUserService
    .getByRoleType(roleType)
    .then((users) => {
      logRequest(req, `Fetched users with role type ${roleType}`, "READ");
      res.json(users);
    })
    .catch(next);
}

function validateEmail(req, res, next) {
  portalUserService
    .validateEmail(req.query.email)
    .then(() => {
      logRequest(req, `Validated email: ${req.query.email}`, "VALIDATE");
      res.json({ status: true });
    })
    .catch(next);
}

function requestPasswordReset(req, res, next) {
  portalUserService
    .requestPasswordReset(req.body)
    .then(() => {
      logRequest(
        req,
        `Requested password reset for ${req.body.email}`,
        "RESET"
      );
      res.json({
        status: true,
        message: "Reset password email sent successfully",
      });
    })
    .catch(next);
}
function sendVerificationEmail(req, res, next) {
  portalUserService
    .sendVerificationEmail(req.query.email)
    .then(() =>
      res.json({
        status: true,
        message: "Email verification link sent successfully",
      })
    )
    .catch(next);
}
function verifyEmail(req, res, next) {
  portalUserService
    .verifyEmail(req.query.token, req.query.email)
    .then(() =>
      res.json({
        status: true,
        message: "Email verified successfully",
      })
    )
    .catch(next);
}

function doPasswordReset(req, res, next) {
  portalUserService
    .doPasswordReset(req.body)
    .then(() => {
      logRequest(req, `Password reset for ${req.body.email}`, "RESET");
      res.json({ status: true, message: "Password changed successfully" });
    })
    .catch(next);
}
