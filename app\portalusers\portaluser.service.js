﻿const config = require("../../config.json");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcryptjs");
const db = require("../_helpers/db");
const utils = require("../_helpers/util.js");
// const emailTemplates = require("../_helpers/templates/templates");
const emailService = require("../send-email/send-email.service.js");
const ngoService = require("../ngos/ngo.service.js");
const { format } = require("date-fns");

const {
  TYPE_MOBILE,
  ROLE_TELE_CALLER,
  CEO_ROLE,
  DIRECTOR_ROLE,
  ADMIN_ROLE,
  TEAM_LEADER_ROLE,
} = require("../constants");
const { Op, where } = require("sequelize");

db.PortalUser.belongsTo(db.Role, {
  as: "roleInfo",
  through: "roles",
  foreignKey: "role_id",
  otherKey: "role_id",
});
db.PortalUser.belongsTo(db.Ngo, {
  as: "ngoInfo",
  through: "ngos",
  foreignKey: "ngo_id",
  otherKey: "ngo_id",
});

module.exports = {
  authenticate,
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  getByRoleType,
  validateEmail,
  doPasswordReset,
  getByNGOId,
  requestPasswordReset,
  getDRStaffMembers,
  getAssignedStaffMembers,
  sendVerificationEmail,
  verifyEmail,
};

async function authenticate({ email, password }) {
  const user = await db.PortalUser.scope("withHash").findOne({
    where: { email },
    include: [
      {
        model: db.Role,
        as: "roleInfo",
        attributes: ["id", "name", "permissions", "type"],
      },
    ],
  });
  if (!user) throw "User not found in System";
  if (!(await bcrypt.compare(password, user.password))) {
    throw "Email or Password is incorrect";
    return;
  }
  if (user && user.status === "Inactive") {
    throw "Your account is Inactive. Please contact administrator for more information";
    return;
  }

  if (user.ngo_id) {
    const ngo = await db.Ngo.findByPk(user.ngo_id, {
      attributes: ["ngo_status"], // exclude if needed
    });


    if (ngo) {
      user.setDataValue("ngo_status", ngo.ngo_status);
    }
  }
  // authentication successful
  const token = jwt.sign({ sub: user.id }, config.secret, {
    expiresIn: "7d",
  });

  return {
    user: { ...omitHash(user.get()) },
    serviceToken: token,
  };
}

async function getAll(params) {
  return await db.PortalUser.findAll({
    include: [
      {
        model: db.Role,
        as: "roleInfo",
        attributes: ["id", "name", "type", "permissions"],
      },
    ],
    order: [["id", "DESC"]],
  });
}

async function getById(id) {
  return await getUser(id);
}

async function create(params) {
  // Validate email uniqueness

  const existingUser = await db.PortalUser.findOne({
    where: { email: params.email },
  });

  if (existingUser) {
    throw `Email "${params.email}" is already taken`;
  }

  // Hash password if provided
  if (params.password) {
    params.password = await bcrypt.hash(params.password, 10);
  }

  // Save user
  const record = await db.PortalUser.create(params);

  // Send email only if status is "Active"
  if (params.status === "Active") {
    const emailTemplate = await db.CommunicationEmail.findOne({
      where: { id: 5 },
    });

    if (!emailTemplate) {
      throw "Email template not found.";
    }

    const fullName = record.fullname;
    const emailBody = utils.replacePlaceholders(emailTemplate.template, {
      fullname: fullName,
    });
  }

  const ngoRecord = await db.Ngo.findOne({
    where: {
      id: params.ngo_id,
    },
  });

  if (
    params.status === "Inactive" &&
    params.role_id === 12 &&
    ngoRecord.dataValues.source === "Internet"
  ) {
    // await ngoService.patch(params?.ngo_id, {
    //   source: "Matched",
    //   claimed_ngo: "yes",
    // });

    //ngo email template
    const emailTemplate = await db.CommunicationEmail.findOne({
      where: {
        id: 4,
      },
    });

    //do right template
    const dorightemailTemplate = await db.CommunicationEmail.findOne({
      where: {
        id: 5,
      },
    });

    if (emailTemplate && dorightemailTemplate) {
      const name = `${params.fullname}`;
      ///body for ngo
      const emailBody = utils.replacePlaceholders(emailTemplate.template, {
        name: name,
        ngo_name: ngoRecord.dataValues.name,
      });

      //body for doright
      const dorightemailBody = utils.replacePlaceholders(
        dorightemailTemplate.template,
        {
          name: name,
          ngo_name: ngoRecord.dataValues.name,
        }
      );
    }
  }

  return record;
}

async function fetchUserIds(usersList, managerIds) {
  let newList = [];
  for (const managerId of managerIds) {
    const listOfUsers = await db.PortalUser.findAll({
      where: { managerId },
      attributes: ["id"],
      raw: true,
    });
    newList = [...newList, ...listOfUsers.map((node) => node.id)];
  }
  if (newList && newList.length > 0) {
    return await fetchUserIds(usersList, newList);
  }
  usersList = [...usersList, ...newList];
  return usersList;
}

async function updateUsersStatus(id, status) {
  let listOfUsersId = [];
  const listOfUsers = await fetchUserIds(listOfUsersId, [id]);
  await db.PortalUser.update(
    { status },
    {
      where: {
        id: {
          [Op.in]: listOfUsers,
        },
      },
    }
  );
}

async function update(id, params) {
  const user = await getUser(id);

  // validate
  const emailChanged = params.email && user.email !== params.email;
  if (
    emailChanged &&
    (await db.PortalUser.findOne({ where: { email: params.email } }))
  ) {
    throw 'email "' + params.email + '" is already taken';
  }

  // hash password if it was entered
  if (params.password) {
    params.password = await bcrypt.hash(params.password, 10);
  }

  const ngoRecord = await db.Ngo.findOne({
    where: {
      id: params.ngo_id,
    },
  });

  if (
    params.status === "Active" &&
    params.role_id === 12 &&
    ngoRecord.dataValues.source === "Internet"
  ) {
    const currentTime = format(new Date(), "yyyy-MM-dd HH:mm:ss");

    await ngoService.patch(params?.ngo_id, {
      source: "Matched",
      claimed_ngo: "yes",
      matched_on: currentTime,
    });

    //ngo email template
    const emailTemplate = await db.CommunicationEmail.findOne({
      where: {
        id: 2,
      },
    });

    //do right template
    // const dorightemailTemplate = await db.CommunicationEmail.findOne({
    //   where: {
    //     id: 4,
    //   },
    // });

    if (emailTemplate) {
      const name = `${params.fullname}`;
      ///body for ngo
      const emailBody = utils.replacePlaceholders(emailTemplate.template, {
        fullname: name,
        ngo_name: ngoRecord.dataValues.name,
      });

      //body for doright
      // const dorightemailBody = utils.replacePlaceholders(
      //   emailTemplate.template,
      //   {
      //     name: name,
      //     ngo_name: ngoRecord.dataValues.name,
      //   }
      // );

      const emailStatus = await utils.sendEmail(
        `${params.email}`,
        `${emailTemplate.subject}`,
        emailBody
      );

      // const dorightemailStatus = await utils.sendEmail(
      //   `<EMAIL>`,
      //   `${dorightemailTemplate.subject}`,
      //   dorightemailBody
      // );
      if (!emailStatus) {
        throw "We were unable to send the email. Please retry again.";
      }
    }
  }

  // copy params to user and save

  Object.assign(user, params);
  await user.save();

  return omitHash(user.get());
}

async function _delete(id) {
  const user = await getUser(id);
  await user.destroy();
}

async function getByRoleType(roleType) {
  const role = await db.Role.findOne({ where: { type: roleType } });
  if (!role) throw `Role type "${roleType}" not found`;

  return await db.PortalUser.findAll({
    order: [["id", "DESC"]],
    where: {
      role_id: {
        [Op.in]: [9, 10, 11],
      },
    },
    include: [
      {
        model: db.Role,
        as: "roleInfo",
        attributes: ["id", "name"],
      },
    ],
  });
}

async function getByNGOId(ngo) {
  return await db.PortalUser.findAll({
    order: [["id", "DESC"]],
    where: {
      ngo_id: ngo.ngoId,
    },
    include: [
      {
        model: db.Role,
        as: "roleInfo",
        attributes: ["id", "name"],
      },
    ],
  });
}

async function getDRStaffMembers() {
  return await db.PortalUser.findAll({
    order: [["id", "DESC"]],
    where: {
      role_id: {
        [Op.in]: [9, 10, 11, 12],
      },
    },
    include: [
      {
        model: db.Role,
        as: "roleInfo",
        attributes: ["id", "name"],
      },
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name"],
      },
    ],
  });
}
async function getAssignedStaffMembers(params) {
  const { portalUserId } = params;

  const ngoRecords = await db.Ngo.findAll({
    where: { assignee_id: portalUserId },
  });

  const extractedNgoRecords = ngoRecords.map(({ dataValues }) => dataValues);
  const ngoRecordIds = extractedNgoRecords.map((ngo) => ngo.id);

  return await db.PortalUser.findAll({
    order: [["id", "DESC"]],
    where: {
      ngo_id: {
        [Op.in]: ngoRecordIds,
      },
    },
    include: [
      {
        model: db.Role,
        as: "roleInfo",
        attributes: ["id", "name"],
      },
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name"],
      },
    ],
  });
}
// helper functions

async function getUser(id) {
  const user = await db.PortalUser.findOne({
    where: { id },
    include: [
      {
        model: db.Role,
        as: "roleInfo",
        attributes: ["id", "name", "permissions"],
      },
    ],
  });
  if (!user) throw "User not found";
  return user;
}

function omitHash(user) {
  const { password, ...userWithoutHash } = user;
  return userWithoutHash;
}

async function validateEmail(email) {
  const user = await db.PortalUser.findOne({ where: { email } });
  if (!user) {
    throw "User not found";
    return;
  }
  if (user && user.status === "Inactive") {
    throw "Your account is Inactive. Please contact administrator for more information";
    return;
  }
  return user;
}

async function requestPasswordReset(params) {
  const clientURL =
    config.environment === "PROD"
      ? config.clientUrl_PROD
      : config.clientUrl_UAT;
  const emailTemplate = await db.CommunicationEmail.findOne({
    where: {
      id: 3,
    },
  });

  let record = await validateEmail(params?.email);
  if (record && emailTemplate) {
    let random = (Math.random() + 1).toString(36).substring(7);
    // let random = Math.floor(100000 + Math.random() * 900000);
    token = await bcrypt.hash(random, 10);
    // token = random;
    record.token = token;
    await record.save();
    const link = `${clientURL}/auth/reset-password?email=${record.email}&token=${token}&id=${record.id}`;
    const fullName = `${record.fullname}`;
    const emailBody = utils.replacePlaceholders(emailTemplate.template, {
      fullname: fullName,
      link: link,
    });

    const emailStatus = await utils.sendEmail(
      `${record.email}`,
      `${emailTemplate.subject}`,
      emailBody
    );
    if (!emailStatus) {
      throw "We were unable to send the email. Please retry again.";
    } else {
      return true;
    }
  }
}

async function doPasswordReset(params) {
  if (!params.email) {
    throw "Please enter email address";
  }
  if (!params.token) {
    throw "Looks like something is wrong with your request. Please try again.";
  }
  if (!params.password) {
    throw "Please enter password";
  }
  if (!params.confirmPassword) {
    throw "Please enter confirm password";
  }
  let record = await db.PortalUser.findOne({
    where: {
      email: params.email,
      token: params.token,
    },
  });
  if (!record) {
    throw "User not found";
    return;
  }
  if (record) {
    const password = await bcrypt.hash(params.password, 10);
    record.password = password;
    record.token = null;
    await record.save();
    // const link = `${clientURL}/passwordReset?token=${resetToken}&id=${user._id}`;
  }
}

async function sendVerificationEmail(email) {
  const user = await db.PortalUser.findOne({ where: { email } });
  if (!user) {
    throw "User not found";
  }

  if (user.status === "Inactive") {
    throw "Your account is Inactive. Please contact administrator for more information";
  }

  // Generate a verification token
  const verificationToken = await bcrypt.hash(email + Date.now(), 10);
  user.verificationToken = verificationToken;
  await user.save();

  const clientURL =
    config.environment === "PROD"
      ? config.clientUrl_PROD
      : config.clientUrl_UAT;

  const verificationLink = `${clientURL}/auth/verify-email?token=${verificationToken}&email=${encodeURIComponent(
    email
  )}`;

  const emailTemplate = await db.CommunicationEmail.findOne({
    where: { id: 8 },
  }); // Assuming template ID 6 is for verification

  if (!emailTemplate) {
    throw "Email template not found.";
  }

  const emailBody = utils.replacePlaceholders(emailTemplate.template, {
    fullname: user?.fullname,
    link: verificationLink,
  });

  const emailStatus = await utils.sendEmail(
    user.email,
    emailTemplate.subject,
    emailBody
  );
  if (!emailStatus) {
    throw "Failed to send verification email. Please try again.";
  }

  return true;
}

async function verifyEmail(token, email) {
  const user = await db.PortalUser.findOne({
    where: { email },
  });

  if (!user) {
    throw "Invalid or expired verification link.";
  }

  user.emailVerificationToken = token; // Clear token
  user.isEmailVerified = "yes"; // Mark as verified
  await user.save();

  const ngo = await db.Ngo.findOne({
    where: { email },
  });

  if (!ngo) {
    throw "No ngo found for the given email";
  }

  if (ngo) {
    ngo.isEmailVerified = "yes";
    await ngo.save();
  }

  return "Email successfully verified!";
}
