const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    name: { type: DataTypes.STRING, allowNull: false },
    image_name: { type: DataTypes.STRING, allowNull: false },
    description: { type: DataTypes.TEXT, allowNull: true },
    slug: { type: DataTypes.STRING, allowNull: false },
    banner_image_width: { type: DataTypes.STRING, allowNull: false },
    banner_image_height: { type: DataTypes.STRING, allowNull: false },
    banner_image_count: { type: DataTypes.INTEGER, allowNull: false },
    gallery_image_width: { type: DataTypes.STRING, allowNull: false },
    gallery_image_height: { type: DataTypes.STRING, allowNull: false },
    gallery_image_count: { type: DataTypes.INTEGER, allowNull: false },
    impact_areas_count: { type: DataTypes.INTEGER, allowNull: false },
    createdBy: { type: DataTypes.INTEGER, allowNull: false },
    updatedBy: { type: DataTypes.INTEGER, allowNull: true },
    createdAt: { type: DataTypes.DATE, allowNull: false },
    updatedAt: { type: DataTypes.DATE, allowNull: false },
    status: { type: DataTypes.STRING, allowNull: false },
  };
  const options = {
    defaultScope: {
      // exclude hash by default
    },
    scopes: {
      // include hash with this scope
      withHash: { attributes: {} },
    },
  };

  return sequelize.define("themes", attributes, options);
}
