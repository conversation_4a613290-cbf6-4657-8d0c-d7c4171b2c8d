const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const themeBannerImageService = require("./themes-banner-image.service");
const multer = require("multer");
const { logAction } = require("../_helpers/logger");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, __basedir + "uploads/banner-images");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

const uploadConfig = multer({ storage: storage });

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", uploadConfig.single("file"), updateSchema, create);
router.get("/:id", getById);
router.put("/:id", uploadConfig.single("file"), updateSchema, update);
router.delete("/:id", _delete);
router.delete("/user/:user_id", deleteByUserId);

module.exports = router;

function create(req, res, next) {
  themeBannerImageService
    .create(req.body)
    .then((record) => {
      logRequest(
        req,
        `Created a new theme banner image with alt text: '${record?.alt_text}', NGO ID: ${record?.ngo_id}`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  themeBannerImageService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all theme banner images", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  themeBannerImageService
    .getById(req.params.id)
    .then((record) => {
      logRequest(
        req,
        `Fetched theme banner image with alt text: '${record?.alt_text}', NGO ID: ${record?.ngo_id}`,
        "READ"
      );
      res.json(record);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    image_url: Joi.string().required(),
    alt_text: Joi.string().required(),
    position: Joi.number().integer().required(),
    ngo_id: Joi.number().allow("", null, "null"),
    createdBy: Joi.number().integer().required(),
    updatedBy: Joi.number().integer().optional(),
    status: Joi.string().max(20).required(),
  });

  validateRequest(req, next, schema);
}

function update(req, res, next) {
  themeBannerImageService
    .update(req.params.id, req.body)
    .then((record) => {
      logRequest(
        req,
        `Updated theme banner image with alt text: '${record?.alt_text}', NGO ID: ${record?.ngo_id}`,
        "UPDATE"
      );
      res.json(record);
    })
    .catch(next);
}

function _delete(req, res, next) {
  themeBannerImageService
    .delete(req.params.id)
    .then(() => {
      logRequest(
        req,
        `Deleted theme banner image with ID: ${req.params.id}`,
        "DELETE"
      );
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}

function deleteByUserId(req, res, next) {
  themeBannerImageService
    .deleteByUserId(req.params.user_id)
    .then(() => {
      logRequest(
        req,
        `Deleted theme banner images for user ID: ${req.params.user_id}`,
        "DELETE"
      );
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
