﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const emailService = require("./send-email.service");

// routes
router.post("/send", sendEmail);

module.exports = router;

function sendEmail(req, res, next) {
  const { email, name, message } = req.body;

  // Validate request body
  if (!email || !name || !message) {
    return res.status(400).json({ message: "Missing required fields." });
  }

  emailService
    .sendEmailToUser(email, name, message)
    .then(() => res.json({ status: true, message: "Email sent successfully" }))
    .catch(next);
}
