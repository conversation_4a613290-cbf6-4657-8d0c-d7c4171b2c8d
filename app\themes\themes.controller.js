const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const themeService = require("./theme.service");
const multer = require("multer");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/themes";
    cb(null, __basedir + "uploads/themes/");
  },
  filename: (req, file, cb) => {
    // const fileName = file.originalname.toLowerCase().split(" ").join("-");
    cb(null, file.originalname);
  },
});

// STAND ALONE CONFIG
const uploadConfig = multer({
  storage: storage,
  // fileFilter: (req, file, cb) => {
  // 	if (
  // 		file.mimetype == "application/pdf"
  // 	) {
  // 		cb(null, true);
  // 	} else {
  // 		cb(null, false);
  // 		return cb(new Error("Only .pdf format allowed!"));
  // 	}
  // },
});
// routes
router.get("/", getAll);
router.post("/", uploadConfig.single("file"), updateSchema, create);
router.get("/:id", getById);
router.put("/:id", uploadConfig.single("file"), updateSchema, update);
router.delete("/:id", _delete);
router.delete("/user/:user_id", deleteByUserId);

module.exports = router;

function create(req, res, next) {
  themeService
    .create(req.body)
    .then(() =>
      res.json({ status: true, message: "Record created successfully" })
    )
    .catch(next);
}

function getAll(req, res, next) {
  themeService
    .getAll(req.query, req?.headers?.isadmin)
    .then((records) => {
      // return Promise.all(records.map(async element => {
      //     element.agentList = await db.Broker.count({ where: { themeId: 3, brokerId: element.id } });
      //     return element;
      // })).then(() => {
      //     res.json(records);
      //     next();
      // });

      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  themeService
    .getById(req.params.id)
    .then((theme) => res.json(theme))
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    image_name: Joi.string().required(),
    description: Joi.string().optional(),
    banner_image_width: Joi.string().required(),
    banner_image_height: Joi.string().required(),
    banner_image_count: Joi.number().integer().required(),
    gallery_image_count: Joi.string().required(),
    gallery_image_height: Joi.string().required(),
    gallery_image_width: Joi.number().integer().required(),
    impact_areas_count: Joi.number().integer().required(),
    createdBy: Joi.number().integer().required(),
    updatedBy: Joi.number().integer().allow("", null),
    status: Joi.string().required(),
  });

  validateRequest(req, next, schema);
}

function update(req, res, next) {
  themeService
    .update(req.params.id, req.body)
    .then((theme) => res.json(theme))
    .catch(next);
}

function _delete(req, res, next) {
  themeService
    .delete(req.params.id)
    .then(() =>
      res.json({ status: true, message: "Record deleted successfully" })
    )
    .catch(next);
}
function deleteByUserId(req, res, next) {
  themeService
    .deleteByUserId(req.params.user_id)
    .then(() => {
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
