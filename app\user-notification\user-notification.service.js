﻿const { Op } = require("sequelize");
const db = require("../_helpers/db");
const firebase = require("firebase-admin");

const firebaseConfig = require("../../drstaging-27e29-firebase-adminsdk-fbsvc-30031ea5b6.json");

firebase.initializeApp({
  credential: firebase.credential.cert(firebaseConfig),
});

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
};

db.UserNotification.belongsTo(db.Category, {
  as: "categoryInfo",
  through: "categories",
  foreignKey: "category_id",
  otherKey: "category_id",
});

async function getAll(params) {
  const { userId } = params;
  const where = {};

  if (userId) {
    where.type_id = {
      [Op.or]: [userId, 0],
    };
  }

  return await db.UserNotification.findAll({
    order: [["id", "DESC"]],
    include: [
      {
        model: db.Category,
        as: "categoryInfo",
        attributes: ["id", "name", "description"],
      },
    ],
    where,
  });
}

async function sendNotification(userTokens, title, body) {
  if (!userTokens || userTokens.length === 0) {
    throw "No device tokens found for provided user IDs.";
  }

  const tokens = userTokens.map((token) => token.device_token);

  try {
    const response = await sendPushNotification(tokens, {
      title: title || "New Notification",
      description: body || "You have a new message.",
    });

    return response;
  } catch (error) {
    console.error("Error in sendNotification function:", error);
    throw "Failed to send push notifications.";
  }
}

async function sendPushNotification(firebaseTokens, params) {
  const notificationPayload = {
    title: params.title || "Default Title",
    body: params.description || "Default body.",
  };

  try {
    const response = await firebase.messaging().sendEachForMulticast({
      tokens: firebaseTokens,
      notification: notificationPayload,
    });

    return response;
  } catch (error) {
    console.error("Error calling sendEachForMulticast:", error);
    throw error;
  }
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const tokens = await db.UserToken.findAll({
    attributes: ["device_token"],
  });

  const deviceTokens = tokens.map((token) => token.device_token);

  await sendNotification(deviceTokens, params.title, params.body);
  const record = await db.UserNotification.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper
async function getSingleRecord(id) {
  const record = await db.UserNotification.findByPk(id);
  if (!record) throw "Notification not found";
  return record;
}
