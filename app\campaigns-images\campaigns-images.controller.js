const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const campaignImageService = require("./campaign-image.service");
const multer = require("multer");

const { logAction } = require("../_helpers/logger");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/campaign-images";
    cb(null, __basedir + "uploads/campaign-images/");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

const uploadConfig = multer({
  storage: storage,
});

// routes
router.get("/:campaignId", getAll);
router.post("/", uploadConfig.single("file"), updateSchema, create);
// router.get("/:id", getById);
router.put("/:id", uploadConfig.single("file"), updateSchema, update);
router.put("/promoImages/:id", updatePromoImages);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  campaignImageService
    .create(req.body)
    .then(() =>
      res.json({ status: true, message: "Record created successfully" })
    )
    .catch(next);
}

function getAll(req, res, next) {
  const campaignId = req.params.campaignId;

  campaignImageService
    .getAll(campaignId)
    .then((records) => {
      logAction(
        5,
        `Fetched all images of campaign with ID ${campaignId}`,
        req.query.pageName
      );
      return res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  campaignImageService
    .getById(req.params.id)
    .then((portalUserCategory) => res.json(portalUserCategory))
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    fileName: Joi.string().required(),
    type: Joi.string().optional().allow("", null),
    priority: Joi.number().optional().allow(null),
    markedForDeletion: Joi.string().optional().allow("", null),
    campaign_id: Joi.number().required(),
    description: Joi.string().optional().allow("", null),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  campaignImageService
    .update(req.params.id, req.body)
    .then((portalUserCategory) => res.json(portalUserCategory))
    .catch(next);
}
function updatePromoImages(req, res, next) {
  campaignImageService
    .updatePromoImages(req.params.id, req.body)
    .then((portalUserCategory) => res.json(portalUserCategory))
    .catch(next);
}

function _delete(req, res, next) {
  campaignImageService
    .deleteBycampaignId(req.params.id)
    .then(() =>
      res.json({ status: true, message: "Records deleted successfully" })
    )
    .catch(next);
}
