const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
};
db.RSVPInfo.belongsTo(db.Ngo, {
  as: "ngoInfo",
  through: "ngos",
  foreignKey: "ngo_id",
  otherKey: "ngo_id",
});
db.RSVPInfo.belongsTo(db.Campaign, {
  as: "campaignInfo",
  through: "campaigns",
  foreignKey: "campaign_id",
  otherKey: "campaign_id",
});

async function getAll(params) {
  const where = {};
  const { ngo_id, campaign_id, rsvpValue } = params;
  if (ngo_id) where.ngo_id = ngo_id;
  if (campaign_id) where.campaign_id = campaign_id;
  if (rsvpValue) where.rsvp_value = rsvpValue;
  return await db.RSVPInfo.findAll({
    where,
    order: [["id", "DESC"]],
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name"],
      },
      {
        model: db.Campaign,
        as: "campaignInfo",
        attributes: ["id", "name"],
      },
    ],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const record = await db.RSVPInfo.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to RSVPInfo and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.RSVPInfo.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
