﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
};

async function getAll(params, isadmin) {
  const whereCondition = isadmin === "yes" ? {} : { status: "Active" };

  return await db.Slide.findAll({ where: whereCondition });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const record = await db.Slide.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.Slide.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
