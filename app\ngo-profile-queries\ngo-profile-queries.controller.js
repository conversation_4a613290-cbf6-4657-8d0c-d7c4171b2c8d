﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const profileQueriesService = require("./ngo-profile-query.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  profileQueriesService
    .create(req.body)
    .then((record) => {
      logRequest(
        req,
        `Created a new profile query with ngo_id: ${record?.ngo_id}, type: ${record?.type}, description: ${record?.description}`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  profileQueriesService
    .getAll(req.query, req?.headers?.isadmin)
    .then((records) => {
      logRequest(req, "Fetched all profile queries", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  profileQueriesService
    .getById(req.params.id)
    .then((record) => {
      logRequest(
        req,
        `Fetched profile query with ngo_id: ${record?.ngo_id}, type: ${record?.type}`,
        "READ"
      );
      res.json(record);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    ngo_id: Joi.number().optional().allow(null),
    portal_user_id: Joi.number().optional().allow(null),
    user_id: Joi.number().optional().allow(null),
    description: Joi.string().allow(null, ""),
    type: Joi.string().allow(null, ""),
    status: Joi.string().allow(null, ""),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  profileQueriesService
    .update(req.params.id, req.body)
    .then((record) => {
      logRequest(
        req,
        `Updated profile query with ngo_id: ${record?.ngo_id}, type: ${record?.type}, description: ${record?.description}`,
        "UPDATE"
      );
      res.json(record);
    })
    .catch(next);
}

function _delete(req, res, next) {
  profileQueriesService
    .delete(req.params.id)
    .then(() => {
      logRequest(
        req,
        `Deleted profile query with ID: ${req.params.id}`,
        "DELETE"
      );
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
