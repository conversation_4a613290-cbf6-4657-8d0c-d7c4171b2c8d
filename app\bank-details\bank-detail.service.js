﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
};

db.BankDetail.belongsTo(db.Ngo, {
  as: "ngoInfo",
  through: "ngos",
  foreignKey: "ngo_id",
  otherKey: "ngo_id",
});

async function getAll(params, isadmin) {
  const where = {};
  const { ngoId } = params;
  if (ngoId) {
    where.ngo_id = ngoId;
  }
  return await db.BankDetail.findAll({
    order: [["id", "DESC"]],
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name", "ngo_status"],
      },
    ],
    where: where,
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // validate

  const record = await db.BankDetail.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.BankDetail.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
