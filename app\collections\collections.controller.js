﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const collectionService = require("./collection.service");
const { logAction } = require("../_helpers/logger");
const multer = require("multer");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/collection-images";
    cb(null, __basedir + "uploads/collection-images/");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

// STAND ALONE CONFIG
const uploadConfig = multer({
  storage: storage,
});

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", uploadConfig.single("file"), create);
router.get("/:id", getById);
router.put("/:id", uploadConfig.single("file"), update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  collectionService
    .create(req.body)
    .then((record) => {
      logRequest(req, `Created a new collection`, "CREATE");
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  collectionService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all collections", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  collectionService
    .getById(req.params.id)
    .then((record) => {
      logRequest(req, `Fetched collection with ID: ${req.params.id}`, "READ");
      res.json(record);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    description: Joi.string().required(),
    imageName: Joi.string().required(),
    status: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  collectionService
    .update(req.params.id, req.body)
    .then((record) => {
      logRequest(req, `Updated collection with ID: ${req.params.id}`, "UPDATE");
      res.json(record);
    })
    .catch(next);
}

function _delete(req, res, next) {
  collectionService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted collection with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
