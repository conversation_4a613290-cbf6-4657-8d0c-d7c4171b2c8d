﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const ngoService = require("./ngo.service");
const { generateSlug } = require("../_helpers/util");
const multer = require("multer");
const { logAction } = require("../_helpers/logger");

function logRequest(req, action, type, ngo) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  const ngoDetails = ngo ? ` (Name: ${ngo.name}, Email: ${ngo.email})` : "";
  logAction(userId, `${action}${ngoDetails}`, pageName, type);
}

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/ngoProfileImages";
    cb(null, __basedir + "uploads/ngoProfileImages/");
  },
  filename: (req, file, cb) => {
    // const fileName = file.originalname.toLowerCase().split(" ").join("-");
    cb(null, file.originalname);
  },
});

const uploadConfig = multer({
  storage: storage,
  // fileFilter: (req, file, cb) => {
  // 	if (
  // 		file.mimetype == "application/pdf"
  // 	) {
  // 		cb(null, true);
  // 	} else {
  // 		cb(null, false);
  // 		return cb(new Error("Only .pdf format allowed!"));
  // 	}
  // },
});

// routes
router.get("/", getAll);
router.get("/allNgos", getAllNgos);
router.get("/search", getNGOs);
router.get("/featured", getFeaturedNGOs);
router.get("/filters", getFilteredNGOs);
router.get("/status/:statusParam", getNGOUsersByStatusParam);
router.get("/assignee/:assigneeID", getByAssigneeId);
router.post("/", registerSchema, create);
router.post("/getAllNGOs", getNgosByFilters);
router.post("/searchByLocationOrCategoryOrGrade", getNgosByFilters);
router.get("/:id", getById);
router.put("/:id", uploadConfig.single("file"), updateSchema, update);
router.patch("/docs/:id", uploadConfig.single("file"), patch);
router.patch("/updateFullInfo", updateFullInfo);
router.patch("/updateFullInfo/name", updateFullInfoByName);
router.get("/ngoStats/allStats", getNGOStats);
router.get("/ngoStats/fullNgoStats", getFullNGOStats);
router.get("/search/searchBySlugAndPhone", getNgoBySlugAndPhone);
router.get("/search/searchByDarpanId", searchByDarpanId);
router.get("/ngoFilters", applyNgoFilters);

router.delete("/:id", _delete);
router.patch("/assignAdmin/:assignee_id", storeByAssineeIds);
router.patch("/mark/featured", markAsFeatured);
router.patch("/inactiveNgo/:id", inactiveNgo);

router.post("/deleteMultipleNgos", deleteMultipleNgos);

module.exports = router;

function create(req, res, next) {
  const { name, pageName } = req.body;
  ngoService
    .create(req.body)
    .then((ngo) => {
      logRequest(req, "Created NGO", "CREATE", ngo);
      return res.json({
        status: true,
        message: "Record created successfully",
        ngoRecord: ngo,
      });
    })
    .catch(next);
}

function getAll(req, res, next) {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const status = req.query.status || "All";
  const assignee_id = parseInt(req.query.assignee_id) || null;
  const filters = req.query;

  ngoService
    .getAll(page, limit, status, assignee_id, filters)
    .then((result) => {
      logRequest(req, "Fetched all NGOs", "READ");

      res.json(result);
    })
    .catch(next);
}

function getById(req, res, next) {
  ngoService
    .getById(req.params.id)
    .then((ngo) => {
      logRequest(req, "Fetched NGO", "READ", ngo);
      return res.json(ngo);
    })
    .catch(next);
}

function registerSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().allow(null, ""),
    email: Joi.string().required(),
    darpan_id: Joi.string().required(),
    pan: Joi.string().required(),
    ngo_type: Joi.string().required(),
    point_of_contact_name: Joi.string().allow(null, ""),
    panNgoName: Joi.string().allow(null, ""),
    point_of_contact_mobile_number: Joi.string().required(),
    ngo_status: Joi.string().required(),
    source: Joi.string().allow(null, ""),
  });
  validateRequest(req, next, schema);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    email: Joi.string().required(),
    pan: Joi.string().optional(),
    darpan_id: Joi.string().allow(null, ""),
    tweleve_number: Joi.string().allow(null, ""),
    eighty_g_number: Joi.string().allow(null, ""),
    type_of_ngo: Joi.string().allow(null, ""),
    website_url: Joi.string().allow(null, ""),
    panNgoName: Joi.string().allow(null, ""),
    point_of_contact_name: Joi.string().allow(null, ""),
    point_of_contact_mobile_number: Joi.string().required(),
    registered_address: Joi.string().allow(null, ""),
    current_address: Joi.string().allow(null, ""),
    date_of_establishment: Joi.date().allow(null, ""),
    reassigned_on: Joi.date().allow(null, ""),
    verified_on: Joi.date().allow(null, ""),
    assigned_on: Joi.date().allow(null, ""),
    latitude: Joi.allow(null, ""),
    longitude: Joi.allow(null, ""),
    pincode: Joi.string().allow(null, ""),
    state: Joi.string().allow(null, ""),
    place_name: Joi.string().allow(null, ""),
    claimed_ngo: Joi.string().allow(null, ""),
    vision: Joi.string().allow(null, ""),
    mission: Joi.string().allow(null, ""),
    tagline: Joi.string().allow(null, ""),
    about_us: Joi.string().allow(null, ""),
    custom_address: Joi.string().allow(null, ""),
    ngo_status: Joi.string().required(),
    last_status: Joi.string().allow(null, ""),
    physical_evidence: Joi.string().allow(null, ""),
    documents_verified: Joi.string().allow(null, ""),
    grade: Joi.string().allow(null, ""),
    rating: Joi.number().allow(null, ""),
    sourceInfo: Joi.allow(null, ""),
    registrationInfo: Joi.allow(null, ""),
    members: Joi.allow(null, ""),
    infor: Joi.allow(null, ""),
    operational_sectors: Joi.string().allow(null, ""),
    nr_city_name: Joi.string().allow(null, ""),
    ngo_type: Joi.string().required(),
    country: Joi.string().allow(null, ""),
    source: Joi.string().allow(null, ""),
    isfeatured: Joi.string().allow(null, ""),
    twitter_url: Joi.string().allow(null, ""),
    youtube_url: Joi.string().allow(null, ""),
    facebook_url: Joi.string().allow(null, ""),
    instagram_url: Joi.string().allow(null, ""),
    fileName: Joi.string().allow(null, ""),
    registration_details: Joi.string().allow(null, ""),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  const { pageName } = req.query;
  ngoService
    .update(req.params.id, req.body)
    .then((user) => {
      logRequest(req, "Updated NGO", "UPDATE", user);

      return res.json(user);
    })
    .catch(next);
}
function patch(req, res, next) {
  const { pageName } = req.query;

  ngoService
    .patch(req.params.id, req.body)
    .then((user) => {
      logRequest(req, "Patched NGO", "UPDATE", user);

      return res.json(user);
    })
    .catch(next);
}

function inactiveNgo(req, res, next) {
  ngoService
    .inactiveNgo(req.params.id)
    .then((user) => {
      logRequest(req, "Inactivated NGO", "INACTIVE", user);

      return res.json(user);
    })
    .catch(next);
}

function _delete(req, res, next) {
  ngoService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted NGO with ID ${req.params.id}`, "DELETE");

      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}

function storeByAssineeIds(req, res, next) {
  const { assignee_id } = req.params;
  ngoService
    .storeByAssineeIds(req.body, assignee_id)
    .then(() => {
      logRequest(req, `Assigned NGOs to Assignee ID ${assignee_id}`, "UPDATE");
      res.json({ message: "NGOs Assigned successfully" });
    })
    .catch(next);
}

function markAsFeatured(req, res, next) {
  const { ngoId, featureMode } = req.query;
  ngoService
    .markAsFeatured(ngoId, featureMode)
    .then(() => {
      logRequest(
        req,
        `NGO ID ${ngoId} ${
          featureMode === "add" ? "marked as" : "removed from"
        } featured`,
        "UPDATE"
      );
      res.json({
        statusText: "OK",
        message: `NGO ${
          featureMode === "add" ? "marked as" : "removed from"
        } featured successfully`,
      });
    })
    .catch(next);
}

function getNGOUsersByStatusParam(req, res, next) {
  const statusParam = req.params.statusParam;
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;

  ngoService
    .getNGOUsersByStatusParam(statusParam, page, limit)
    .then((result) => {
      logRequest(req, `Fetched NGOs by Status: ${statusParam}`, "READ");
      res.json(result);
    })
    .catch(next);
}

// function getByAssigneeId(req, res, next) {
//   const assigneeID = req.params.assigneeID;
//   ngoService
//     .getByAssigneeId(assigneeID)
//     .then((users) => res.json(users))
//     .catch(next);
// }
function getByAssigneeId(req, res, next) {
  const assigneeID = req.params.assigneeID;
  const status = req.query.status;
  ngoService
    .getByAssigneeId(assigneeID, status)
    .then((users) => {
      logRequest(req, "Fetched NGOs by Assignee ID", "READ");
      res.json(users);
    })
    .catch(next);
}

function getNGOs(req, res, next) {
  ngoService
    .getNGOs(req.query)
    .then((records) => {
      logRequest(req, "Fetched NGOs by search criteria", "READ");
      res.json(records);
    })
    .catch(next);
}

function getFeaturedNGOs(req, res, next) {
  ngoService
    .getFeaturedNGOs()
    .then((records) => {
      logRequest(req, "Fetched featured NGOs", "READ");
      res.json(records);
    })
    .catch(next);
}

function getAllNgos(req, res, next) {
  ngoService
    .getAllNgos()
    .then((records) => {
      logRequest(req, "Fetched all NGOs", "READ");
      res.json(records);
    })
    .catch(next);
}

function updateFullInfo(req, res, next) {
  const { darpan_id } = req.query;

  if (!darpan_id) {
    return res.status(400).json({ error: "Missing id in request body" });
  }

  ngoService
    .patchFullInfoByDarpanId(darpan_id, req.body)
    .then((updatedRecord) => {
      logRequest(
        req,
        `Updated full NGO info by Darpan ID: ${darpan_id}`,
        "UPDATE",
        updatedRecord
      );
      res.json({
        status: true,
        message: `NGO updated successfully for darpan_id ${darpan_id}`,
        updatedRecord,
      });
    })
    .catch(next);
}

function updateFullInfoByName(req, res, next) {
  const { name } = req.query;

  if (!name) {
    return res.status(400).json({ error: "Missing name in request body" });
  }

  ngoService
    .patchFullInfoByName(name, req.body)
    .then((updatedRecord) => {
      logRequest(
        req,
        `Updated full NGO info by Name: ${name}`,
        "UPDATE",
        updatedRecord
      );
      res.json({
        status: true,
        message: `NGO updated successfully for name ${name}`,
        updatedRecord,
      });
    })
    .catch(next);
}

function getNGOStats(req, res, next) {
  const { assignee_id } = req.query;
  ngoService
    .getNGOStats(assignee_id)
    .then((stats) => {
      logRequest(req, "Fetched NGO Stats", "READ");
      res.json(stats);
    })
    .catch(next);
}

function getFullNGOStats(req, res, next) {
  ngoService
    .getFullNGOStats()
    .then((stats) => {
      logRequest(req, "Fetched Full NGO Stats", "READ");
      res.json(stats);
    })
    .catch(next);
}

function getNgoBySlugAndPhone(req, res, next) {
  const { name, phone_number } = req.query;
  const slug = generateSlug(name);
  ngoService
    .getNgoBySlugAndPhone(slug, phone_number)
    .then((ngo) => {
      logRequest(req, "Fetched NGO by Slug and Phone", "READ", ngo);
      res.json(ngo);
    })
    .catch(next);
}

function searchByDarpanId(req, res, next) {
  const { darpanId } = req.query;
  ngoService
    .searchByDarpanId(darpanId)
    .then((ngo) => {
      logRequest(req, "Searched NGO by Darpan ID", "READ", ngo);
      res.json(ngo);
    })
    .catch(next);
}

function getNgosByFilters(req, res, next) {
  const filters = req.body;

  if (!filters.latitude || !filters.longitude) {
    return res.status(400).json({
      status: false,
      message: "Latitude and Longitude are required for this operation",
    });
  }

  ngoService
    .getNgosByFilters(filters)
    .then((ngos) => {
      logRequest(req, "Fetched NGOs by Filters", "READ");
      res.json(ngos);
    })
    .catch(next);
}

function getFilteredNGOs(req, res, next) {
  const filters = req.query;

  ngoService
    .getFilteredNGOs(filters)
    .then((ngos) => {
      logRequest(req, "Fetched Filtered NGOs", "READ");
      res.json(ngos);
    })
    .catch(next);
}
function applyNgoFilters(req, res, next) {
  const filters = req.query;

  ngoService
    .applyNgoFilters(filters)
    .then((ngos) => {
      logRequest(req, "Fetched Filtered NGOs", "READ");
      res.json(ngos);
    })
    .catch(next);
}

function deleteMultipleNgos(req, res, next) {
  ngoService
    .deleteMultipleNgos(req.body)
    .then(() => {
      logRequest(req, "Deleted Multiple NGOs", "DELETE");
      res.json({ status: true, message: "NGOs deleted successfully" });
    })
    .catch(next);
}
