﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const ordersService = require("./order.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.post("/donate", donate);
router.get("/:id", getById);
router.put("/:id", update);
router.patch("/:id", patch);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  ordersService
    .create(req.body)
    .then((response) => {
      logRequest(
        req,
        `Created a new order with order_prefix: ${req.body.order_prefix}, quantity: ${req.body.quantity}, total_price: ${req.body.total_price}, order_status: ${req.body.order_status}, payment_status: ${req.body.payment_status}, user_id: ${req.body.user_id}`,
        "CREATE"
      );
      res.json({
        status: true,
        message: "Record created successfully",
        order: response,
      });
    })
    .catch(next);
}

function donate(req, res, next) {
  ordersService
    .donate(req.body)
    .then((result) => {
      logRequest(
        req,
        `Donated order with order_prefix: ${req.body.order_prefix}, quantity: ${req.body.quantity}, total_price: ${req.body.total_price}, order_status: ${req.body.order_status}, payment_status: ${req.body.payment_status}, user_id: ${req.body.user_id}`,
        "CREATE"
      );
      res.json({
        status: true,
        message: "Record created successfully",
        data: result,
      });
    })
    .catch(next);
}

function getAll(req, res, next) {
  ordersService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all orders", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  ordersService
    .getById(req.params.id)
    .then((user) => {
      logRequest(req, `Fetched order with ID: ${req.params.id}`, "READ");
      res.json(user);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    order_prefix: Joi.string().optional(),
    product_id: Joi.number().integer().optional().allow(null),
    bucket_id: Joi.number().integer().optional().allow(null),
    user_id: Joi.number().integer().required(),
    ngo_id: Joi.array().items(Joi.number().integer()).optional(),
    Campaigns: Joi.array().items(Joi.number().integer()).optional(),

    quantity: Joi.number().integer().min(1).optional().allow(null),
    price_per_unit: Joi.number().precision(2).optional().allow(null),
    total_price: Joi.number().precision(2).required(),
    order_status: Joi.string().optional().allow(null),
    payment_status: Joi.string().required(),
    shipping_address: Joi.string().optional().allow(null),
    billing_address: Joi.string().optional().allow(null),
    shipping_cost: Joi.number().precision(2).allow(null),
    discount_applied: Joi.number().precision(2).allow(null),
    remarks: Joi.string().allow(null),
    type: Joi.string().allow(null),
    donationFor: Joi.allow(null),
    campaign_id: Joi.number().integer().optional().allow(null),
    purpose: Joi.string().allow(null),
    purpose_donationday: Joi.string().allow(null),

  });

  validateRequest(req, next, schema);
}

function update(req, res, next) {
  ordersService
    .update(req.params.id, req.body)
    .then((user) => {
      logRequest(
        req,
        `Updated order with ID: ${req.params.id}, order_prefix: ${req.body.order_prefix}, quantity: ${req.body.quantity}, total_price: ${req.body.total_price}, order_status: ${req.body.order_status}, payment_status: ${req.body.payment_status}, user_id: ${req.body.user_id}`,
        "UPDATE"
      );
      res.json(user);
    })
    .catch(next);
}

function patch(req, res, next) {
  ordersService
    .patch(req.params.id, req.body)
    .then((user) => {
      logRequest(
        req,
        `Patched order with ID: ${req.params.id}, order_prefix: ${req.body.order_prefix}, quantity: ${req.body.quantity}, total_price: ${req.body.total_price}, order_status: ${req.body.order_status}, payment_status: ${req.body.payment_status}, user_id: ${req.body.user_id}`,
        "UPDATE"
      );
      res.json(user);
    })
    .catch(next);
}

function _delete(req, res, next) {
  ordersService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted order with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
