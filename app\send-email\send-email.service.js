﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");

const { contactUsEmailTemplate, activationEmailTemplate } = require("../_helpers/templates/templates");

module.exports = {
  sendEmailToUser,
};

async function sendEmailToUser(email, name, message, templateType) {
  //get the complete body
  const params = {
    name,
    email,
    message,
  };

  let emailBody;

  switch (templateType) {
    case "activation":
      emailBody = activationEmailTemplate(params);
      break;
    default:
      emailBody = contactUsEmailTemplate(params);
  }

  // Send the email
  const emailStatus = await utils.sendEmail(
    "<EMAIL>",
    name,
    emailBody
  );

  if (!emailStatus) {
    throw new Error("Failed to send email. Please try again later.");
  }

  return { message: "Email sent successfully." };
}
