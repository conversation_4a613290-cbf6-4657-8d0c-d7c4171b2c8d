const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const ngoImageService = require("./ngo-image.service");
const { logAction } = require("../_helpers/logger");

const multer = require("multer");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/ngo-images";
    cb(null, __basedir + "uploads/ngo-images/");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

const uploadConfig = multer({
  storage: storage,
});

// routes
router.get("/:ngoId", getAll);
router.post("/", uploadConfig.single("file"), updateSchema, create);
// router.get("/:id", getById);
router.put("/:id", uploadConfig.single("file"), updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  ngoImageService
    .create(req.body)
    .then(() =>
      res.json({ status: true, message: "Record created successfully" })
    )
    .catch(next);
}

function getAll(req, res, next) {
  const ngoId = req.params.ngoId;

  ngoImageService
    .getAll(ngoId)
    .then((records) => {
      logAction(
        5,
        `Fetched all images of NGO with ID ${ngoId}`,
        req.query.pageName
      );
      return res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  ngoImageService
    .getById(req.params.id)
    .then((portalUserCategory) => res.json(portalUserCategory))
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    fileName: Joi.string().required(),
    ngo_id: Joi.number().required(),
    description: Joi.string().optional().allow("", null),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  ngoImageService
    .update(req.params.id, req.body)
    .then((portalUserCategory) => res.json(portalUserCategory))
    .catch(next);
}

function _delete(req, res, next) {
  ngoImageService
    .deleteByNGOId(req.params.id)
    .then(() =>
      res.json({ status: true, message: "Records deleted successfully" })
    )
    .catch(next);
}
