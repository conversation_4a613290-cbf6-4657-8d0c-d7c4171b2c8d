﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
// const stateService = require("./state.service");

// routes
router.get("/", getGrades);
router.get("/:id", getGradeById);

// router.post("/", updateSchema, create);
// router.get("/:id", getById);
// router.put("/:id", updateSchema, update);
// router.delete("/:id", _delete);

module.exports = router;

const gradesData = [
  { id: 1, name: "Gold", slug: "gold", status: "active" },
  { id: 2, name: "Silver", slug: "silver", status: "active" },
  { id: 3, name: "Bronze", slug: "bronze", status: "active" },
  { id: 4, name: "New", slug: "new", status: "active" },
];

function getGrades(req, res, next) {
  try {
    res.json([
      { id: 1, name: "Gold", slug: "gold", status: "active" },
      { id: 2, name: "Silver", slug: "silver", status: "active" },
      { id: 3, name: "Bronze", slug: "bronze", status: "active" },
      { id: 4, name: "New", slug: "new", status: "active" },
    ]);
  } catch (error) {
    next(error);
  }
}
function getGradeById(req, res, next) {
  try {
    const id = parseInt(req.params.id);
    const grade = gradesData.find((g) => g.id === id);

    if (!grade) {
      res.status(404).json({ status: false, message: "Grade not found" });
    } else {
      res.json(grade);
    }
  } catch (error) {
    next(error);
  }
}
// function create(req, res, next) {
//   stateService
//     .create(req.body)
//     .then(() =>
//       res.json({ status: true, message: "Record created successfully" })
//     )
//     .catch(next);
// }

// function getAll(req, res, next) {
//   stateService
//     .getAll(req.query)
//     .then((records) => {
//       // return Promise.all(records.map(async element => {
//       //     element.agentList = await db.Broker.count({ where: { stateId: 3, brokerId: element.id } });
//       //     return element;
//       // })).then(() => {
//       //     res.json(records);
//       //     next();
//       // });

//       res.json(records);
//     })
//     .catch(next);
// }

// function getById(req, res, next) {
//   stateService
//     .getById(req.params.id)
//     .then((user) => res.json(user))
//     .catch(next);
// }

// function updateSchema(req, res, next) {
//   const schema = Joi.object({
//     name: Joi.string().required(),
//     status: Joi.string().required(),
//   });
//   validateRequest(req, next, schema);
// }

// function update(req, res, next) {
//   stateService
//     .update(req.params.id, req.body)
//     .then((user) => res.json(user))
//     .catch(next);
// }

// function _delete(req, res, next) {
//   stateService
//     .delete(req.params.id)
//     .then(() =>
//       res.json({ status: true, message: "Record deleted successfully" })
//     )
//     .catch(next);
// }
