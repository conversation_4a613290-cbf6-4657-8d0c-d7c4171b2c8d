const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    name: { type: DataTypes.STRING, allowNull: false },
    description: { type: DataTypes.TEXT, allowNull: false },
    isStock: { type: DataTypes.STRING, allowNull: false },

    discountedPrice: { type: DataTypes.DECIMAL, allowNull: true },
    discount: { type: DataTypes.DECIMAL, allowNull: true },
    imageName: { type: DataTypes.STRING, allowNull: false, unique: true },
    ngo_id: { type: DataTypes.INTEGER, allowNull: true },
    count: { type: DataTypes.INTEGER, allowNull: false },
    status: { type: DataTypes.STRING, allowNull: true },
    collection_id: { type: DataTypes.INTEGER, allowNull: true },
    price: { type: DataTypes.DECIMAL, allowNull: false },
    unit_of_measure: { type: DataTypes.STRING, allowNull: true },
  };

  //   Product name - All details about product user can enter
  // Product Description - More details about product user can enter
  // Stock
  // Unity of measure -
  // Price -
  // Discount Price -
  // Discounted Price -
  // Cover image, Other images

  const options = {
    defaultScope: {
      // exclude hash by default
    },
    scopes: {
      // include hash with this scope
      withHash: { attributes: {} },
    },
  };

  return sequelize.define("products", attributes, options);
}
