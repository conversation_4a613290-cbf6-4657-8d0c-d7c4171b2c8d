const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const campaignCategoryService = require("./campaign-category-location.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  campaignCategoryService
    .create(req.body)
    .then((category) => {
      logRequest(
        req,
        `Created a new campaign category ${category?.name}`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  campaignCategoryService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, `Fetched all campaign categories`, "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  campaignCategoryService
    .getById(req.params.id)
    .then((campaignCategory) => {
      logRequest(
        req,
        `Fetched campaign category ${campaignCategory?.name}`,
        "READ"
      );
      res.json(campaignCategory);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    status: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  campaignCategoryService
    .update(req.params.id, req.body)
    .then((campaignCategory) => {
      logRequest(
        req,
        `Updated campaign category ${campaignCategory?.name}`,
        "UPDATE"
      );
      res.json(campaignCategory);
    })
    .catch(next);
}

function _delete(req, res, next) {
  campaignCategoryService
    .delete(req.params.id)
    .then(() => {
      logRequest(
        req,
        `Deleted campaign category with ID: ${req.params.id}`,
        "DELETE"
      );
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
