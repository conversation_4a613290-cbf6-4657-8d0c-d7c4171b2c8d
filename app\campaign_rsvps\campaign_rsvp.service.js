const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const campaignService = require("../campaigns/campaign.service");
module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  getUpcomingUserEventsForUser,
};

db.CampaignRsvps.belongsTo(db.Campaign, {
  as: "campaignInfo",
  through: "campaigns",
  foreignKey: "campaign_id",
  otherKey: "campaign_id",
});
db.CampaignRsvps.belongsTo(db.User, {
  as: "userInfo",
  through: "users",
  foreignKey: "user_id",
  otherKey: "user_id",
});

async function getAll(params) {
  const where = {};
  const { campaignId, rvspValue, userId } = params;
  if (campaignId) where.campaign_id = campaignId;
  if (rvspValue) where.rsvp_value = rvspValue;
  if (userId) {
    where.user_id = userId;
    where.rsvp_value = "yes";
  }

  return await db.CampaignRsvps.findAll({
    where,
    order: [["id", "DESC"]],
    include: [
      {
        model: db.Campaign,
        as: "campaignInfo",
        attributes: ["id", "name"],
      },
      {
        model: db.User,
        as: "userInfo",
        attributes: ["id", "fullname", "gender", "age", "skills"],
      },
    ],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const record = await db.CampaignRsvps.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to CampaignRsvps and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.CampaignRsvps.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}

async function getUpcomingUserEventsForUser(params) {
  const campaigns = await campaignService.getUpcomingCampaigns({
    type: "Event",
  });
  const campaignIds = campaigns.map((c) => c.id);


  const rsvpRecords = await db.CampaignRsvps.findAll({
    where: {
      user_id: params.userId,
      rsvp_value: "yes",
      campaign_id: campaignIds,
    },
    include: [
      {
        model: db.Campaign,
        as: "campaignInfo",
        attributes: [
          "id",
          "name",
          "fileName",
          "address",
          "event_time",
          "event_date",
          "description",
        ],
      },
    ],
  });
  rsvpRecords.sort(
    (a, b) =>
      new Date(a.campaignInfo.event_date) - new Date(b.campaignInfo.event_date)
  );

  return params.isAll === "yes" ? rsvpRecords : rsvpRecords.slice(0, 3);
}
