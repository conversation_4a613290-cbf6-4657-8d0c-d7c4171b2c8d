﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const jwt = require("jsonwebtoken");
const config = require("../../config.json");
const { default: axios } = require("axios");
const bcrypt = require("bcryptjs");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  generateOtp,
  verifyOtp,
  sendVerificationOTP,
  verifyOtpForNgo,
  sendVerificationEmail,
  verifyEmail,
  resetPassword,
  getImpactCreated,
};

async function getAll() {
  return await db.User.findAll();
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const loginType = params?.loginType || "mobile";
  const where = {};
  if (loginType === "mobile") {
    where.mobile_number = params?.mobile_number;
  } else {
    where.email = params?.email;
  }
  const checkIfRecordExists = await db.User.findOne({
    where: where,
  });
  const otp = utils.generateOtp();
  if (!checkIfRecordExists?.id) {
    // send otp
    // params.otp = otp;
    if (loginType === "mobile" || loginType === "email") {
      params.otp = otp;
    }
    if (loginType === "email" && params.password) {
      params.password = await bcrypt.hash(params.password, 10);
    }
    const userRecord = await db.User.create(params);
    if (loginType === "mobile" || loginType === "email") {
      await generateOtp(params, loginType);
    }
    return { isNewRegistration: true, ...userRecord?.dataValues };
  } else {
    Object.assign(checkIfRecordExists, { otp: otp });
    await checkIfRecordExists.save();
    if (loginType === "mobile") {
      await generateOtp(checkIfRecordExists, loginType);
      return true;
    } else {
      const token = jwt.sign({ sub: checkIfRecordExists.id }, config.secret, {
        expiresIn: "7d",
      });
      return { ...checkIfRecordExists?.dataValues, token };
    }
  }
}

async function sendVerificationOTP(params) {
  const checkIfRecordExists = await db.Ngo.findOne({
    where: { point_of_contact_mobile_number: params?.mobile_number },
  });

  if (!checkIfRecordExists) {
    return { status: false, message: "No NGO found for this mobile number" };
  }

  const otp = utils.generateOtp();
  params.otp = otp;

  const response = await generateOtp(params);

  if (response) {
    return {
      status: true,
      message: "OTP has been sent to your mobile number",
    };
  } else {
    return {
      status: false,
      message: "Something went wrong, please try again later",
    };
  }
}

async function verifyOtp(params) {
  const loginType = params?.loginType || "mobile";
  const where = {};
  if (loginType === "mobile") {
    where.mobile_number = params?.mobile_number;
    where.otp = params.otp;
  } else {
    where.email = params?.email;
    where.otp = params.otp;
  }
  const checkIfRecordExists = await db.User.findOne({
    where: where,
  });
  if (!checkIfRecordExists?.id) {
    return { status: true, message: "No user found", user: null };
  } else {
    const token = jwt.sign({ sub: checkIfRecordExists.id }, config.secret, {
      expiresIn: "7d",
    });
    return { status: true, user: checkIfRecordExists, token };
  }
}

async function verifyOtpForNgo(params) {
  const checkIfRecordExists = await db.Ngo.findOne({
    where: { point_of_contact_mobile_number: params?.mobile_number },
  });
  if (!checkIfRecordExists?.id) {
    return { status: false, message: "No Ngo found", user: null };
  } else {
    const token = jwt.sign({ sub: checkIfRecordExists.id }, config.secret, {
      expiresIn: "7d",
    });
    return { status: true, user: checkIfRecordExists };
  }
}

async function sendVerificationEmail(email) {
  const params = { email: email };
  const user = await db.User.findOne({ where: { email } });
  if (!user) {
    throw "User not found";
  }

  const otp = utils.generateOtp();
  params.emailotp = otp;

  const [updatedCount] = await db.User.update(
    { emailotp: otp },
    {
      where: { email: params.email },
    }
  );

  if (updatedCount === 0) {
    return {
      status: false,
      message: "No user found for this email or OTP not saved",
    };
  }

  const emailTemplate = await db.CommunicationEmail.findOne({
    where: { id: 9 },
  });

  if (!emailTemplate) {
    throw "Email template not found.";
  }

  const emailBody = utils.replacePlaceholders(emailTemplate.template, {
    fullname: user?.fullname,
    otp: otp,
  });

  const emailStatus = await utils.sendEmail(
    user.email,
    emailTemplate.subject,
    emailBody
  );
  if (!emailStatus) {
    throw "Failed to send verification email. Please try again.";
  }

  return true;
}

async function verifyEmail(params) {
  const checkIfRecordExists = await db.User.findOne({
    where: { email: params?.email, emailotp: params?.emailotp },
  });
  if (!checkIfRecordExists?.id) {
    return { status: false, message: "Verification failed", user: null };
  } else {
    const token = jwt.sign({ sub: checkIfRecordExists.id }, config.secret, {
      expiresIn: "7d",
    });
    return { status: true, user: checkIfRecordExists, token };
  }
}

async function resetPassword({ id, currentPassword, newPassword, source }) {
  const user = await db.User.findByPk(id);
  if (!user) {
    throw new Error("User not found");
  }

  if (source === "userprofile") {
    if (!currentPassword) {
      throw new Error("Current password is required");
    }

    const isMatch = await bcrypt.compare(currentPassword, user.password);
    if (!isMatch) {
      throw new Error("Current password is incorrect");
    }
  }

  const hashedPassword = await bcrypt.hash(newPassword, 10);
  user.password = hashedPassword;
  await user.save();

  return {
    status: true,
    message:
      source === "userprofile"
        ? "Password updated successfully"
        : "Password reset successfully",
  };
}

async function update(id, params) {
  // copy params to user and save
  const record = await getSingleRecord(id);
  params.status = "Active";
  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  record.status = "Inactive";
  await record.save();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.User.findByPk(id);
  if (!record) throw "Record not found";

  const totalFields = [
    "fullname",
    "gender",
    "mobile_number",
    "monthlyDonationGoal",
    "pincode",
    "state",
    "age",
    "about",
    "skills",
    "token",
    "otp",
    "dob",
    "email",
    "password",
    "pan",
    "occupation",
    "citizenship",
    "interests",
    "purposes",
    "city",
    "address_line_1",
    "address_line_2",
  ];

  let filledFields = totalFields.filter(
    (field) => record[field] && record[field] !== ""
  ).length;

  let completionPercentage = Math.round(
    (filledFields / totalFields.length) * 100
  );

  record.setDataValue("completionPercentage", completionPercentage);
  return record;
  //   return { ...record.get(), completionPercentage };
}

async function generateOtp(params, loginType = "mobile") {
  if (loginType === "mobile") {
    const message = encodeURIComponent(
      `${params.otp} is the OTP to verify your mobile number with DoRight. Please do not share this OTP with anyone.-DORGHT`
    );
    await axios.get(
      `https://sms6.rmlconnect.net:8443/bulksms/bulksms?username=zensocio&password=y8lG-%5BD7&type=0&dlr=1&destination=91${params?.mobile_number}&source=DORGHT&message=${message}&entityid=1201173131450491942&tempid=1207173349829291055`
    );
    return true;
  } else {
    // const emailTemplate = await db.CommunicationEmail.findOne({
    //     where: {
    //         id: 2,
    //     },
    // });
    // if (emailTemplate) {
    const name = `${params.fullname}`;
    ///body for ngo
    const emailBody = `<h2 style="color: #333;">Login Verification</h2>
        <p style="font-size: 16px; color: #555;">Hi ${name},</p>
        <p style="font-size: 16px; color: #555;">
          Use the one-time password (OTP) below to log in to your account.</strong>.
        </p>
        <div style="text-align: center; margin: 30px 0;">
          <span style="font-size: 32px; letter-spacing: 8px; font-weight: bold; color: #000;">${params?.otp}</span>
        </div>
        <p style="font-size: 14px; color: #999;">
          If you did not request this login, you can safely ignore this email.
        </p>
        <p style="font-size: 16px; color: #555;">Thanks,<br/>The DoRight Team</p>`;

    const emailStatus = await utils.sendEmail(
      `${params.email}`,
      `Your One-Time Password (OTP) for Login`,
      emailBody
    );

    if (!emailStatus) {
      return false;
    }
    return true;
  }
  // }
}

async function getImpactCreated({ userId }) {
  const user = await db.User.findByPk(userId);
  if (!user) throw new Error("User not found");

  // Fallback to default value
  const monthlyDonationGoal = user.monthlyDonationGoal || 40;
  const totalImpactCreated = user.total_impact_created || 10;

  // Fetch all user's transactions
  const transactions = await db.Transaction.findAll({
    where: { user_id: userId },
  });

  let totalContribution = 0;
  let campaignCount = 0;
  const categoryContribution = {}; // { categoryId: totalAmount }

  for (const txn of transactions) {
    const amount = Number(txn.amount || 0);
    totalContribution += amount;

    if (txn.donation_type === "campaign" && txn.campaign_id) {
      campaignCount++;

      const campaign = await db.Campaign.findByPk(txn.campaign_id);
      if (campaign?.category_id) {
        const catId = campaign.category_id;
        categoryContribution[catId] =
          (categoryContribution[catId] || 0) + amount;
      }
    }
  }

  // Calculate percentage per category
  const causesImpacted = [];
  const totalForPercentage = Object.values(categoryContribution).reduce(
    (a, b) => a + b,
    0
  );

  for (const [categoryId, amount] of Object.entries(categoryContribution)) {
    const percentage = ((amount / totalForPercentage) * 100).toFixed(2);
    const category = await db.Category.findByPk(categoryId);
    causesImpacted.push({
      category: category?.name || `Category ${categoryId}`,
      percentage: Number(percentage),
    });
  }

  return {
    monthlyDonationGoal,
    impactScore: totalImpactCreated,
    totalContribution,
    campaignCount,
    hoursSpent: 2, // static for now
    livesImpacted: 10, // static for now
    causesImpacted,
  };
}
