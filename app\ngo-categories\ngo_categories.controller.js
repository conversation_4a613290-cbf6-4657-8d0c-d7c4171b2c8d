const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const ngoCategoryService = require("./ngo_category.service");
const { logAction } = require("../_helpers/logger");

// routes
router.get("/", getAll);
router.get("/ngos/:ngo_id", getByNGOId);
router.delete("/ngos/:ngo_id", deleteByNGOId);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  const { pageName } = req.query;

  ngoCategoryService
    .create(req.body)
    .then(() => {
      logAction(5, `Added a Catgories in ${pageName}`, pageName);
      return res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  ngoCategoryService
    .getAll(req.query)
    .then((records) => {
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  ngoCategoryService
    .getById(req.params.id)
    .then((portalUserCategory) => res.json(portalUserCategory))
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    category_id: Joi.number().required(),
    ngo_id: Joi.number().required(),
    subCategories: Joi.string().allow(null, ""),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  ngoCategoryService
    .update(req.params.id, req.body)
    .then((portalUserCategory) => res.json(portalUserCategory))
    .catch(next);
}

function _delete(req, res, next) {
  ngoCategoryService
    .delete(req.params.id)
    .then(() =>
      res.json({ status: true, message: "Record deleted successfully" })
    )
    .catch(next);
}
function getByNGOId(req, res, next) {
  const { pageName } = req.query;
  ngoCategoryService
    .getByNGOId(req.params.ngo_id)
    .then((records) => {
      logAction(
        5,
        `fetched NGO Based Categories for NGO with ngo id ${req.params.ngo_id}`,
        pageName
      );

      return res.json(records);
    })
    .catch(next);
}
function deleteByNGOId(req, res, next) {
  ngoCategoryService
    .deleteByNGOId(req.params.ngo_id)
    .then((records) => {
      res.json(records);
    })
    .catch(next);
}
