﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
};
async function getAll(params, isadmin) {
  const options = {
    where: {},
  };

  if (isadmin !== "yes") {
    options.where.status = "Active";
  }

  return await db.Skill.findAll(options);
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // validate
  params.slug = utils.generateSlug(params.name);
  if (await db.Skill.findOne({ where: { slug: params.slug } })) {
    throw 'Record "' + params.name + '" is already taken';
    return;
  }

  const record = await db.Skill.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  params.slug = utils.generateSlug(params.name);
  // validate

  const recordChanged = params.slug && record.slug !== params.slug;
  if (
    recordChanged &&
    (await db.Skill.findOne({ where: { slug: params.slug } }))
  ) {
    throw 'Skill "' + params.name + '" is already taken';
  }

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.Skill.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
