﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const reviewService = require("./review.service");
const { logAction } = require("../_helpers/logger");

const multer = require("multer");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/review-images";
    cb(null, __basedir + "uploads/review-images/");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

const uploadConfig = multer({
  storage: storage,
});

// routes
router.get("/", getAll);
router.post("/", uploadConfig.single("file"), updateSchema, create);
router.get("/:id", getById);
router.put("/:id", uploadConfig.single("file"), updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  reviewService
    .create(req.body)
    .then(() =>
      res.json({ status: true, message: "Record created successfully" })
    )
    .catch(next);
}

function getAll(req, res, next) {
  reviewService
    .getAll(req.query)
    .then((records) => {
      logAction(
        5,
        `Fetched all reviews in ${req.query.pageName}`,
        req.query.pageName
      );
      return res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  reviewService
    .getById(req.params.id)
    .then((user) => res.json(user))
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    description: Joi.string().required(),
    imageName: Joi.string().optional().allow(null, ""),
    ngoId: Joi.number().required(),
    userId: Joi.number().required(),
    type: Joi.string().required(),
    typeId: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  reviewService
    .update(req.params.id, req.body)
    .then((user) => res.json(user))
    .catch(next);
}

function _delete(req, res, next) {
  reviewService
    .delete(req.params.id)
    .then(() =>
      res.json({ status: true, message: "Record deleted successfully" })
    )
    .catch(next);
}
