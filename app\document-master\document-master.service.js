﻿const { Op, fn, col, Sequelize } = require("sequelize");
const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const path = require("path");
const fs = require("fs");

module.exports = {
  getAll,
  getAllForNgoSearch,
  getById,
  create,
  update,
  delete: _delete,
  deleteAll,
};

async function getAll(params, isadmin) {
  let where = {};

  if (!params?.documentType) {
    throw "Document Type is mandatory";
    return;
  }
  if (params && params?.type && params?.type !== "null") {
    where = { type: params?.type };
  }
  if (params && params?.documentType && params?.documentType !== "undefined") {
    where = {
      ...where,
      [Op.and]: [
        fn("FIND_IN_SET", params?.documentType, col("document_type")), // Replace with your actual column name
      ],
    };
  }
  if (params?.type === "NGO") {
    where.isDeleted = { [Op.or]: ["no", null, ""] };
  }

  if (isadmin !== "yes") {
    where = {
      ...where,
      status: "Active",
    };
  }

  return await db.Document.findAll({
    where: where,
    order: [
      [
        Sequelize.literal("CASE WHEN mandatory = 'yes' THEN 0 ELSE 1 END"),
        "ASC",
      ],
      ["name", "ASC"],
    ],
  });
}

async function getAllForNgoSearch(params) {
  let where = {};

  if (params && params?.type && params?.type !== "null") {
    where = { type: params?.type };
  }
  if (params && params?.documentType && params?.documentType !== "undefined") {
    where = {
      ...where,
      [Op.and]: [
        fn("FIND_IN_SET", params?.documentType, col("document_type")), // Replace with your actual column name
      ],
    };
  }

  return await db.Document.findAll({
    where: where,
    order: [
      [
        Sequelize.literal("CASE WHEN mandatory = 'yes' THEN 0 ELSE 1 END"),
        "ASC",
      ],
      ["name", "ASC"],
    ],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // validate
  // params.slug = utils.generateSlug(params.name);
  // if (await db.Document.findOne({ where: { slug: params.slug } })) {
  //   throw 'Record "' + params.name + '" is already taken';
  //   return;
  // }

  const record = await db.Document.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // params.slug = utils.generateSlug(params.name);
  // // validate

  // const recordChanged = params.slug && record.slug !== params.slug;
  // if (
  //   recordChanged &&
  //   (await db.Document.findOne({ where: { slug: params.slug } }))
  // ) {
  //   throw 'Document "' + params.name + '" is already taken';
  // }

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

async function deleteAll(id) {
  const record = await getSingleRecord(id);

  const documentUserRecords = await db.DocumentUser.findAll({
    where: { documentId: id },
  });

  documentUserRecords.forEach((doc) => {
    const filePath = path.join(__basedir, "uploads/documents", doc.fileName);


    fs.unlink(filePath, (err) => {
      if (err) {
        console.error(`Error deleting file ${doc.fileName}:`, err);
      } else {
        console.log(`Successfully deleted ${doc.fileName}`);
      }
    });
  });

  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.Document.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
