const { Op } = require("sequelize");
const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const { v4: uuidv4 } = require("uuid");
const config = require("../../config.json");

module.exports = {
  getAll,
  getById,
  getLatestByNgoId,
  create,
  update,
  delete: _delete,
  generateUniqueLink,
};

db.KycInformation.belongsTo(db.Ngo, {
  as: "ngoInfo",
  through: "ngos",
  foreignKey: "ngo_id",
  otherKey: "ngo_id",
});

async function generateUniqueLink(ngoId) {
  const uniqueLink = uuidv4(); // Generate a unique identifier

  const ngoRecord = await db.Ngo.findOne({
    where: {
      id: ngoId,
    },
  });

  const emailTemplate = await db.CommunicationEmail.findOne({
    where: {
      id: 10,
    },
  });

  const emailBody = utils.replacePlaceholders(emailTemplate.template, {
    fullname: ngoRecord.dataValues.name,
    video_verification_link: `${config.KYC_VERIFICATION_LINK}/${uniqueLink}`,
  });

  const emailStatus = await utils.sendEmail(
    // ngoRecord.dataValues.email,
    "<EMAIL>",
    emailTemplate.subject,
    emailBody
  );
  if (!emailStatus) {
    throw "We were unable to send the email. Please retry again.";
  }

  const record = await db.KycInformation.create({
    // ngo_id: ngoId,
    ngo_id: 80313,
    status: "Pending",
    uniqueId: uniqueLink,
  });

  return { uniqueLink, id: record.id };
}

async function getAll(params) {
  let where = {};

  if (params && params?.ngoId && params?.ngoId !== "undefined") {
    where.ngo_id = params?.ngoId;
  }

  if (params && params?.status && params?.status !== "undefined") {
    where.status = params?.status;
  }
  if (params && params?.uniqueId) {
    where.uniqueId = params?.uniqueId;
  }

  return await db.KycInformation.findAll({
    where: where,
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: [
          "id",
          "name",
          "ngo_status",
          "darpan_id",
          "current_address",
        ],
      },
    ],
    order: [["id", "DESC"]],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function getLatestByNgoId(ngoId) {
  const record = await db.KycInformation.findOne({
    where: { ngo_id: ngoId },
    order: [["createdAt", "DESC"]], // Get the most recent record
  });

  if (!record) {
    throw "No KYC Information found for this NGO";
  }

  return record;
}

async function create(params) {
  // Check if KYC information already exists for this NGO
  const existingRecord = await db.KycInformation.findOne({
    where: { ngo_id: params.ngo_id },
  });

  if (existingRecord) {
    throw "KYC Information already exists for this NGO";
  }

  const record = await db.KycInformation.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // Copy params to record and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.KycInformation.findByPk(id);
  if (!record) throw "KYC Information record not found";
  return record;
}
