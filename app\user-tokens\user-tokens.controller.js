﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const userTokensService = require("./user-token.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  userTokensService
    .create(req.body)
    .then(() => {
      logRequest(req, `Created a new user token`, "CREATE");
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  userTokensService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, `Fetched all user token`, "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  userTokensService
    .getById(req.params.id)
    .then((record) => {
      logRequest(req, `Fetched user token ${record?.name}`, "READ");
      res.json(record);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    user_id: Joi.number().required(),
    device_token: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  userTokensService
    .update(req.params.id, req.body)
    .then((record) => {
      logRequest(req, `Updated user token `, "UPDATE");
      res.json(record);
    })
    .catch(next);
}

function _delete(req, res, next) {
  userTokensService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted user token with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
