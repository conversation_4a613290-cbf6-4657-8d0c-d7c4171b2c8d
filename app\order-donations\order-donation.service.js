﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
};

db.OrderDonation.belongsTo(db.Campaign, {
  as: "campaignInfo",
  through: "campaigns",
  foreignKey: "campaign_id",
  otherKey: "campaign_id",
});

async function getAll(params, isadmin) {
  const { campaignId, orderId } = params;
  const where = {};
  if (campaignId) {
    where.campaign_id = campaignId;
  }
  if (orderId) {
    where.order_id = orderId;
  }
  return await db.OrderDonation.findAll({
    order: [["id", "DESC"]],
    where: where,
    include: [
      {
        model: db.Campaign,
        as: "campaignInfo",
        attributes: ["id", "name"],
      },
    ],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // validate

  const record = await db.OrderDonation.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(orderId) {
  const records = await db.OrderDonation.findAll({
    where: { order_id: orderId },
  });

  if (!records.length) throw "No records found for the given order ID";

  await db.OrderDonation.destroy({ where: { order_id: orderId } });

  return {
    message: `Deleted ${records.length} records linked to order ID ${orderId}`,
  };
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.OrderDonation.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
