const { where } = require("sequelize");
const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  getByNGOId,
  deleteByNGOId,
};

db.NgoImage.belongsTo(db.Ngo, {
  as: "ngoInfo",
  through: "ngos",
  foreignKey: "ngo_id",
  otherKey: "ngo_id",
});

async function getAll(ngoId) {
  let where = {};
  if (ngoId) {
    where.ngo_id = ngoId;
  }
  const result = await db.NgoImage.findAll({
    where: where,
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name"],
      },
    ],
  });
  if (!result) return "result not found for this status";
  return result;
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const record = await db.NgoImage.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.NgoImage.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}

async function getByNGOId(ngo_id) {
  const record = await db.NgoImage.findAll({
    where: {
      ngo_id,
    },
  });
  if (!record) throw "Record not found";
  return record;
}
async function deleteByNGOId(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}
