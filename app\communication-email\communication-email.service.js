﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  sendTestEmails,
  update,
  delete: _delete,
};

async function getAll(params, isadmin) {
  const options = {
    order: [["id", "DESC"]],
    where: {},
  };

  if (isadmin !== "yes") {
    options.where.status = "Active";
  }

  return await db.CommunicationEmail.findAll(options);
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function sendTestEmails(templateId, emailsList) {
  const emailTemplate = await getById(templateId);

  if (!emailTemplate) {
    throw new Error("Email template not found.");
  }

  const emailBody = utils.replacePlaceholders(emailTemplate.template, {
    fullname: "Test Name",
  });

  for (const email of emailsList) {
    const emailStatus = await utils.sendEmail(
      email,
      emailTemplate.subject,
      emailBody
    );
    if (!emailStatus) {
      throw new Error("We were unable to send the email. Please retry again.");
    }
  }
}

async function create(params) {
  const record = await db.CommunicationEmail.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.CommunicationEmail.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
