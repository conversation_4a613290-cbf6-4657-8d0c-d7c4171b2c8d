﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const cartItemService = require("./cart-item.service");
const { logAction } = require("../_helpers/logger");
const multer = require("multer");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/collection-images";
    cb(null, __basedir + "uploads/collection-images/");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

// STAND ALONE CONFIG
const uploadConfig = multer({
  storage: storage,
});

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  cartItemService
    .create(req.body)
    .then((record) => {
      logRequest(req, `Created a new cart item`, "CREATE");
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  cartItemService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all cart items", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  cartItemService
    .getById(req.params.id)
    .then((record) => {
      logRequest(req, `Fetched cart item with ID: ${req.params.id}`, "READ");
      res.json(record);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    quantity: Joi.number().required(),
    product_id: Joi.number().required(),
    price: Joi.number().required(),
    subtotal: Joi.number().optional().allow(null),
    cart_id: Joi.number().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  cartItemService
    .update(req.params.id, req.body)
    .then((record) => {
      logRequest(req, `Updated cart item with ID: ${req.params.id}`, "UPDATE");
      res.json(record);
    })
    .catch(next);
}

function _delete(req, res, next) {
  cartItemService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted cart item with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
