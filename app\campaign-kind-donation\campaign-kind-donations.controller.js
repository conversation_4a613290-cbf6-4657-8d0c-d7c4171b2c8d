﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const kindDonationService = require("./campaign-kind-donation.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", updateSchema, create);
// router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);
router.delete("/campaign/:campaign_id", _deleteByCampaignId);

module.exports = router;

function create(req, res, next) {
  kindDonationService
    .create(req.body)
    .then((donation) => {
      logRequest(
        req,
        `Created a new donation: Item - ${donation?.item_name}, Quantity - ${donation?.quantity}, Campaign ID - ${donation?.campaign_id}`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  const { campaign_id } = req.query;

  kindDonationService
    .getAll(req.query, campaign_id)
    .then((records) => {
      logRequest(
        req,
        `Fetched all donations for Campaign ID - ${
          campaign_id || "All Campaigns"
        }`,
        "READ"
      );
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  kindDonationService
    .getById(req.params.id)
    .then((donation) => {
      logRequest(
        req,
        `Fetched donation: Item - ${donation?.item_name}, Quantity - ${donation?.quantity}, Campaign ID - ${donation?.campaign_id}`,
        "READ"
      );
      res.json(donation);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    item_name: Joi.string().required(),
    quantity: Joi.number().required(),
    unit_of_measure: Joi.string().required(),
    portal_user_id: Joi.number().integer().required(),
    campaign_id: Joi.number().optional().allow(null),
    createdBy: Joi.number().integer().required(),
    updatedBy: Joi.number().integer().required(),
  });

  validateRequest(req, next, schema);
}

function update(req, res, next) {
  kindDonationService
    .update(req.params.id, req.body)
    .then((donation) => {
      logRequest(
        req,
        `Updated donation: Item - ${donation?.item_name}, Quantity - ${donation?.quantity}, Campaign ID - ${donation?.campaign_id}`,
        "UPDATE"
      );
      res.json(donation);
    })
    .catch(next);
}

function _delete(req, res, next) {
  kindDonationService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted donation with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}

function _deleteByCampaignId(req, res, next) {
  const campaign_id = req.params.campaign_id;

  kindDonationService
    .deleteByCampaignId(campaign_id)
    .then(() => {
      logRequest(
        req,
        `Deleted all donations for Campaign ID: ${campaign_id}`,
        "DELETE"
      );
      res.json({ status: true, message: "All records deleted successfully" });
    })
    .catch(next);
}
