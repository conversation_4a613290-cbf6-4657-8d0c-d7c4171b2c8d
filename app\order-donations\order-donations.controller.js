﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const orderDonationService = require("./order-donation.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  orderDonationService
    .create(req.body)
    .then((record) => {
      logRequest(
        req,
        `Created a new order donation with order_id: ${record?.order_id}, campaign_id: ${record?.campaign_id}`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  orderDonationService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all order donations", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  orderDonationService
    .getById(req.params.id)
    .then((record) => {
      logRequest(
        req,
        `Fetched order donation with order_id: ${record?.order_id}, campaign_id: ${record?.campaign_id}`,
        "READ"
      );
      res.json(record);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    order_id: Joi.number().required(),
    campaign_id: Joi.number().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  orderDonationService
    .update(req.params.id, req.body)
    .then((record) => {
      logRequest(
        req,
        `Updated order donation with order_id: ${record?.order_id}, campaign_id: ${record?.campaign_id}`,
        "UPDATE"
      );
      res.json(record);
    })
    .catch(next);
}

function _delete(req, res, next) {
  orderDonationService
    .delete(req.params.id)
    .then(() => {
      logRequest(
        req,
        `Deleted order donation with ID: ${req.params.id}`,
        "DELETE"
      );
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
