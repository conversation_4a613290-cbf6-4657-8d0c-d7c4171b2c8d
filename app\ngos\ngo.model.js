// import  DataTypes from "sequelize";
const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    name: { type: DataTypes.STRING, allowNull: false },
    slug: { type: DataTypes.STRING, allowNull: false },
    email: { type: DataTypes.STRING, allowNull: false },
    darpan_id: { type: DataTypes.STRING, allowNull: true },
    pan: { type: DataTypes.STRING, allowNull: true },
    tweleve_number: { type: DataTypes.STRING, allowNull: true },
    eighty_g_number: { type: DataTypes.STRING, allowNull: true },
    type_of_ngo: { type: DataTypes.STRING, allowNull: true },
    website_url: { type: DataTypes.STRING, allowNull: true },
    point_of_contact_name: { type: DataTypes.STRING, allowNull: true },
    point_of_contact_mobile_number: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    registered_address: { type: DataTypes.TEXT, allowNull: true },
    current_address: { type: DataTypes.TEXT, allowNull: true },
    date_of_establishment: { type: DataTypes.DATE, allowNull: true },
    reassigned_on: { type: DataTypes.DATE, allowNull: true },
    verified_on: { type: DataTypes.DATE, allowNull: true },
    assigned_on: { type: DataTypes.DATE, allowNull: true },
    latitude: { type: DataTypes.DECIMAL, allowNull: true },
    longitude: { type: DataTypes.DECIMAL, allowNull: true },
    pincode: { type: DataTypes.STRING, allowNull: true },
    state: { type: DataTypes.STRING, allowNull: true },
    place_name: { type: DataTypes.STRING, allowNull: true },
    claimed_ngo: { type: DataTypes.STRING, allowNull: true },
    vision: { type: DataTypes.TEXT, allowNull: true },
    mission: { type: DataTypes.TEXT, allowNull: true },
    tagline: { type: DataTypes.TEXT, allowNull: true },
    about_us: { type: DataTypes.TEXT, allowNull: true },
    custom_address: { type: DataTypes.TEXT, allowNull: true },
    ngo_status: { type: DataTypes.STRING, allowNull: true },
    physical_evidence: { type: DataTypes.STRING, allowNull: true },
    documents_verified: { type: DataTypes.STRING, allowNull: true },
    status: { type: DataTypes.STRING, allowNull: true },
    assignee_id: { type: DataTypes.INTEGER, allowNull: true },
    darpan_last_modified: { type: DataTypes.STRING, allowNull: true },
    grade: { type: DataTypes.STRING, allowNull: true },
    rating: { type: DataTypes.DECIMAL, allowNull: true },
    sourceInfo: { type: DataTypes.TEXT, allowNull: true },
    registrationInfo: { type: DataTypes.TEXT, allowNull: true },
    members: { type: DataTypes.TEXT, allowNull: true },
    infor: { type: DataTypes.TEXT, allowNull: true },
    operational_sectors: { type: DataTypes.TEXT, allowNull: true },
    nr_city_name: { type: DataTypes.STRING, allowNull: true },
    ngo_type: { type: DataTypes.STRING, allowNull: true },
    country: { type: DataTypes.STRING, allowNull: true },
    source: { type: DataTypes.STRING, allowNull: true },
    isfeatured: { type: DataTypes.STRING, allowNull: true },
    registration_2A: { type: DataTypes.STRING, allowNull: true },
    registration_80G: { type: DataTypes.STRING, allowNull: true },
    fcra: { type: DataTypes.STRING, allowNull: true },
    csr_registration_number: { type: DataTypes.STRING, allowNull: true },
    instagram_url: { type: DataTypes.STRING, allowNull: true },
    facebook_url: { type: DataTypes.STRING, allowNull: true },
    youtube_url: { type: DataTypes.STRING, allowNull: true },
    twitter_url: { type: DataTypes.STRING, allowNull: true },
    fileName: { type: DataTypes.STRING, allowNull: true },
    registration_details: { type: DataTypes.STRING, allowNull: true },
    isEmailVerified: { type: DataTypes.STRING, allowNull: true },
    isNumberVerified: { type: DataTypes.STRING, allowNull: true },
    panNgoName: { type: DataTypes.STRING, allowNull: true },
    matched_on: { type: DataTypes.DATE, allowNull: true },
  };

  // registration_2A: '',
  //   registration_80G: '',
  //   fcra: '',
  //   csr_registration_number: '',
  //   instagram_url: '',
  //   facebook_url: '',
  //   youtube_url: '',
  //   twitter_url: ''

  const options = {
    scopes: {
      // include hash with this scope
      withHash: { attributes: {} },
    },
  };

  return sequelize.define("ngos", attributes, options);
}
