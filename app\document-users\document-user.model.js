const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    ngo_id: { type: DataTypes.INTEGER, allowNull: false },
    fileName: { type: DataTypes.STRING, allowNull: false },
    approval_status: { type: DataTypes.STRING, allowNull: false },
    documentId: { type: DataTypes.INTEGER, allowNull: false },
    rejection_reason: { type: DataTypes.TEXT, allowNull: true },
  };

  const options = {
    scopes: {
      // include hash with this scope
      withHash: { attributes: {} },
    },
  };

  return sequelize.define("document_users", attributes, options);
}
