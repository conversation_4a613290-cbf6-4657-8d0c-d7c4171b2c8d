const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  getRecommendedBuckets,
};

db.BucketItem.belongsTo(db.Ngo, {
  as: "ngoInfo",
  through: "ngos",
  foreignKey: "ngo_id",
  otherKey: "ngo_id",
});
db.BucketItem.belongsTo(db.Category, {
  as: "categoryInfo",
  through: "categories",
  foreignKey: "category_id",
  otherKey: "category_id",
});
db.BucketItem.belongsTo(db.Campaign, {
  as: "campaignInfo",
  through: "campaigns",
  foreignKey: "campaign_id",
  otherKey: "campaign_id",
});

async function getAll(params) {
  const whereClause = {};
  if (params.ngoId) {
    whereClause.ngo_id = params.ngoId;
  }
  if (params.campaignId) {
    whereClause.campaign_id = params.campaignId;
  }
  if (params.causeId) {
    whereClause.category_id = params.causeId;
  }

  const buckets = await db.Bucket.findAll({
    where: whereClause,
    order: [["id", "DESC"]],
  });

  return buckets;
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const bucket = await db.Bucket.create({
    name: params.name,
    description: params.description,
  });

  const bucketId = bucket.id;

  const bucketItems = await Promise.all(
    params.allocations.map(async (allocation) => {
      const bucketItem = await db.BucketItem.create({
        bucket_id: bucketId,
        ngo_id: allocation.ngoId,
        campaign_id: allocation.campaignId,
        category_id: allocation.categoryId,
        percentage: allocation.percentage,
      });
      return bucketItem;
    })
  );

  return { bucket, bucketItems };
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // Step 1: Update the bucket fields
  record.name = params.name;
  record.description = params.description;
  await record.save();

  // Step 2: Remove all existing allocations for this bucket
  await db.BucketItem.destroy({ where: { bucket_id: id } });

  // Step 3: Add updated allocations
  const bucketItems = await Promise.all(
    params.allocations.map(async (allocation) => {
      const bucketItem = await db.BucketItem.create({
        bucket_id: id,
        ngo_id: allocation.ngoId,
        campaign_id: allocation.campaignId,
        category_id: allocation.categoryId,
        percentage: allocation.percentage,
      });
      return bucketItem;
    })
  );

  return { bucket: record, bucketItems };
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
  return record;
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.Bucket.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}


async function getRecommendedBuckets(params) {
  let categoryIds = [];

  // Step 1: Decide category IDs based on weDecide flag
  if (params.weDecide === "yes") {
    const user = await db.User.findByPk(params.userId);
    if (!user || !user.interests) throw "User not found or has no interests";

    categoryIds = user.interests.split(",").map((id) => parseInt(id.trim()));
  } else {
    categoryIds = params.categoryIds || [];
  }

  if (categoryIds.length === 0) return [];

  // Step 2: Find matching BucketItems
  const allItems = await db.BucketItem.findAll({
    where: { category_id: categoryIds },
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name", "grade", "fileName"],
      },
      {
        model: db.Category,
        as: "categoryInfo",
        attributes: ["id", "name", "description"],
      },
      {
        model: db.Campaign,
        as: "campaignInfo",
        attributes: ["id", "name", "description", "fileName"],
      },
    ],
  });

  // Count total unique NGOs and campaigns
  const uniqueNgoIds = new Set(allItems.map((item) => item.ngo_id));
  const totalNgos = uniqueNgoIds.size;
  const totalCampaigns = allItems.length;

  const bucketIds = [...new Set(allItems.map((item) => item.bucket_id))];

  // Step 3: Fetch Buckets
  const buckets = await db.Bucket.findAll({
    where: { id: bucketIds },
    order: [["id", "DESC"]],
  });

  // Step 4: Attach enriched data to each bucket
  const result = await Promise.all(
    buckets.map(async (bucket) => {
      const items = allItems.filter((item) => item.bucket_id === bucket.id);

      // Unique NGOs
      const ngoMap = new Map();
      for (const item of items) {
        const ngo = item.ngoInfo;
        if (ngo && !ngoMap.has(ngo.id)) {
          // Get NgoCategory
          const ngoCategory = await db.NgoCategory.findOne({
            where: { ngo_id: ngo.id },
          });

          // Get category name (if exists)
          let categoryName = null;
          if (ngoCategory && ngoCategory.category_id) {
            const category = await db.Category.findByPk(
              ngoCategory.category_id
            );
            categoryName = category ? category.name : null;
          }

          ngoMap.set(ngo.id, {
            ...ngo.toJSON(),
            categoryName,
          });
        }
      }

      // All Campaigns
      const campaigns = items.map((item) => item.campaignInfo);

      // Unique Categories
      const categoryMap = new Map();
      items.forEach((item) => {
        if (item.categoryInfo && !categoryMap.has(item.categoryInfo.id)) {
          categoryMap.set(item.categoryInfo.id, item.categoryInfo);
        }
      });

      return {
        ...bucket.toJSON(),
        // items,
        ngos: Array.from(ngoMap.values()),
        campaigns,
        categories: Array.from(categoryMap.values()),
      };
    })
  );

  return {
    totalNgos,
    totalCampaigns,
    buckets: result,
  };
}
