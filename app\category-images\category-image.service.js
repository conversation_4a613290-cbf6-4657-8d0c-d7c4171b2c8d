const { where } = require("sequelize");
const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  getByCategoryId,
  deleteByCategoryId,
};

db.CategoryImage.belongsTo(db.Category, {
  as: "categoryInfo",
  through: "categories",
  foreignKey: "category_id",
  otherKey: "category_id",
});

async function getAll(categoryId) {
  let where = {};
  if (categoryId) {
    where.category_id = categoryId;
  }
  const result = await db.CategoryImage.findAll({
    where: where,
    include: [
      {
        model: db.Category,
        as: "categoryInfo",
        attributes: ["id", "name"],
      },
    ],
  });
  if (!result) return "result not found for this status";
  return result;
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const record = await db.CategoryImage.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.CategoryImage.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}

async function getByCategoryId(category_id) {
  const record = await db.CategoryImage.findAll({
    where: {
      category_id,
    },
  });
  if (!record) throw "Record not found";
  return record;
}
async function deleteByCategoryId(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}
