const { allow } = require("joi");
const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    name: { type: DataTypes.STRING, allowNull: true },
    description: { type: DataTypes.TEXT, allowNull: true },
    campaign_start_date: { type: DataTypes.DATE, allowNull: true },
    event_time: { type: DataTypes.TIME, allowNull: true },
    campaign_end_date: { type: DataTypes.DATE, allowNull: true },
    sameday_event: { type: DataTypes.STRING, allowNull: true },
    category_id: { type: DataTypes.INTEGER, allowNull: true },
    format: { type: DataTypes.STRING, allowNull: true },
    ngo_id: { type: DataTypes.INTEGER, allowNull: true },
    place_name: { type: DataTypes.STRING, allowNull: true },
    pincode: { type: DataTypes.STRING, allowNull: true },
    country: { type: DataTypes.STRING, allowNull: true },
    current_address: { type: DataTypes.STRING, allowNull: true },

    state: { type: DataTypes.STRING, allowNull: true },
    meeting_link: { type: DataTypes.STRING, allowNull: true },
    latitude: { type: DataTypes.DECIMAL(10, 8), allowNull: true },
    longitude: { type: DataTypes.DECIMAL(11, 8), allowNull: true },
    facebook_url: { type: DataTypes.STRING, allowNull: true },
    instagram_url: { type: DataTypes.STRING, allowNull: true },
    youtube_url: { type: DataTypes.STRING, allowNull: true },
    twitter_url: { type: DataTypes.STRING, allowNull: true },
    promotional_hashtag: { type: DataTypes.STRING, allowNull: true },
    address: { type: DataTypes.STRING, allowNull: true },
    impact_goal: { type: DataTypes.STRING, allowNull: true },
    fund_raising_target: { type: DataTypes.DECIMAL(10, 2), allowNull: true },
    donor_target_type: { type: DataTypes.STRING, allowNull: true },
    volunteers_required: { type: DataTypes.STRING, allowNull: true },
    no_of_volunteers: { type: DataTypes.INTEGER, allowNull: true },
    skills: { type: DataTypes.STRING, allowNull: true },
    createdBy: { type: DataTypes.INTEGER, allowNull: true },
    status: { type: DataTypes.STRING, allowNull: true },
    fileName: { type: DataTypes.STRING, allowNull: true },
    updatedBy: { type: DataTypes.INTEGER, allowNull: true },
    isfeatured: { type: DataTypes.STRING, allowNull: true },
    ratio: { type: DataTypes.STRING, allowNull: true },
    event_type: { type: DataTypes.STRING, allowNull: true },
    fullday_event: { type: DataTypes.STRING, allowNull: true },
    volunteer_type: { type: DataTypes.STRING, allowNull: true },
    number_of_volunteers: { type: DataTypes.INTEGER, allowNull: true },
    amount_per_person: { type: DataTypes.DECIMAL, allowNull: true },
    event_date: { type: DataTypes.DATE, allowNull: true },
    event_end_time: { type: DataTypes.TIME, allowNull: true },
    event_start_time: { type: DataTypes.TIME, allowNull: true },
    imagesUpdated: { type: DataTypes.TIME, allowNull: true },
    eventId: { type: DataTypes.INTEGER, allowNull: true },
    inactivatedBy: { type: DataTypes.INTEGER, allowNull: true },
    inactivatedAt: { type: DataTypes.DATE, allowNull: true },

    //new columns
  };

  const options = {
    defaultScope: {
      // exclude hash by default
    },
    scopes: {
      // include hash with this scope
      withHash: { attributes: {} },
    },
  };

  return sequelize.define("campaigns", attributes, options);
}
