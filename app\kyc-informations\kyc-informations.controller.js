const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const kycInformationService = require("./kyc-information.service");
const { logAction } = require("../_helpers/logger");

// routes
router.get("/", getAll);
router.post("/", createSchema, create);
router.get("/latest-data/:ngoId", getLatestByNgoId);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);
router.get("/generate-link/:ngoId", generateUniqueLink);

module.exports = router;

function generateUniqueLink(req, res, next) {
  const ngoId = req.params.ngoId;
  kycInformationService
    .generateUniqueLink(ngoId)
    .then((data) => res.json(data))
    .catch(next);
}

function create(req, res, next) {
  kycInformationService
    .create(req.body)
    .then(() => {
      logAction(
        5,
        `Added KYC Information for NGO ${req.body.ngo_id} in ${req.query.pageName}`
      );
      return res.json({
        status: true,
        message: "KYC Information created successfully",
      });
    })
    .catch(next);
}

function getAll(req, res, next) {
  kycInformationService
    .getAll(req.query)
    .then((records) => {
      logAction(
        5,
        `Fetched all KYC Information records in ${req.query.pageName}`,
        req.query.pageName
      );
      return res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  kycInformationService
    .getById(req.params.id)
    .then((record) => res.json(record))
    .catch(next);
}

function getLatestByNgoId(req, res, next) {
  kycInformationService
    .getLatestByNgoId(req.params.ngoId)
    .then((record) => {
      logAction(
        5,
        `Fetched latest KYC Information for NGO ${req.params.ngoId} in ${req.query.pageName}`,
        req.query.pageName
      );
      return res.json(record);
    })
    .catch(next);
}

function createSchema(req, res, next) {
  const schema = Joi.object({
    ngo_id: Joi.number().required(),
    status: Joi.string().allow(null, ""),
    verification_link: Joi.string().allow(null, ""),
    uploadToken: Joi.string().allow(null, ""),
    albumId: Joi.string().allow(null, ""),
    uniqueId: Joi.string().allow(null, ""),
    uploadResponse: Joi.string().allow(null, ""),
    createdBy: Joi.number().allow(null, ""),
    updatedBy: Joi.number().allow(null, ""),
  });
  validateRequest(req, next, schema);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    ngo_id: Joi.number(),
    status: Joi.string().allow(null, ""),
    verification_link: Joi.string().allow(null, ""),
    uploadToken: Joi.string().allow(null, ""),
    albumId: Joi.string().allow(null, ""),
    uniqueId: Joi.string().allow(null, ""),
    uploadResponse: Joi.string().allow(null, ""),
    createdBy: Joi.number().allow(null, ""),
    updatedBy: Joi.number().allow(null, ""),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  kycInformationService
    .update(req.params.id, req.body)
    .then((record) => {
      logAction(
        5,
        `Updated KYC Information record ${req.params.id} in ${req.query.pageName}`
      );
      return res.json(record);
    })
    .catch(next);
}

function _delete(req, res, next) {
  kycInformationService
    .delete(req.params.id)
    .then(() => {
      logAction(
        5,
        `Deleted KYC Information record ${req.params.id} in ${req.query.pageName}`
      );
      return res.json({
        status: true,
        message: "KYC Information deleted successfully",
      });
    })
    .catch(next);
}
