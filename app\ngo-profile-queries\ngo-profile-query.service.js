﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
};

db.NgoProfileQueries.belongsTo(db.Ngo, {
  as: "ngoInfo",
  through: "ngos",
  foreignKey: "ngo_id",
  otherKey: "ngo_id",
});
db.NgoProfileQueries.belongsTo(db.User, {
  as: "userInfo",
  through: "users",
  foreignKey: "user_id",
  otherKey: "user_id",
});
db.NgoProfileQueries.belongsTo(db.PortalUser, {
  as: "portaluserInfo",
  through: "portal_users",
  foreignKey: "portal_user_id",
  otherKey: "portal_user_id",
});

async function getAll(params, isadmin) {
  const where = {};
  const { ngoId, userId, type } = params;

  if (userId) {
    const ngoRecords = await db.Ngo.findAll({
      where: { assignee_id: userId },
      attributes: ["id"],
    });

    filteredNgoIds = ngoRecords.map(({ id }) => id);

    if (filteredNgoIds.length > 0) {
      where.ngo_id = filteredNgoIds;
    }
  }

  if (ngoId) {
    where.ngo_id = ngoId;
  }
  if (type) {
    where.type = type;
  }
  return await db.NgoProfileQueries.findAll({
    order: [["id", "DESC"]],
    where: where,
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name"],
      },
      {
        model: db.User,
        as: "userInfo",
        attributes: ["id", "fullname"],
      },
      {
        model: db.PortalUser,
        as: "portaluserInfo",
        attributes: ["id", "fullname"],
      },
    ],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const record = await db.NgoProfileQueries.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.NgoProfileQueries.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
