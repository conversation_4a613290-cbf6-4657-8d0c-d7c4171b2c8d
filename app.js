const express = require("express");
const app = express();
const cors = require("cors");
const bodyParser = require("body-parser");
const Razorpay = require("razorpay");
const crypto = require("crypto");

const path = require("path");
const errorHandler = require("./app/_middleware/error-handler");
global.__basedir = __dirname + "/";
const DIR = "./images";
const PDFDIR_DOCUMENTS = "./uploads/documents/";
const THEME_IMAGES = "./uploads/themes";
const BANNER_IMAGES = "./uploads/banner-images";
const CAMPAIGN_IMAGES = "./uploads/campaign-cover-images";
const CAMPAIGN_MILESTONES_IMAGES = "./uploads/campaigns-milestones";
const NGO_IMAGES = "./uploads/ngoProfileImages";
const NGO_MULTIPLE_IMAGES = "./uploads/ngo-images";
const CAMPAIGN_MULTIPLE_IMAGES = "./uploads/campaign-images";
const PRODUCT_MULTIPLE_IMAGES = "./uploads/product-images";
const CATEGORY_IMAGES = "./uploads/category-images";
const COLLECTION_IMAGES = "./uploads/collection-images";
const SLIDE_IMAGES = "./uploads/slide-images";
const REVIEW_IMAGES = "./uploads/review-images";
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: false }));
app.use(cors());

// api routes
app.get("/", (req, res) => {
  res.json({ message: "Welcome to application." });
});

app.use("/api/roles", require("./app/roles/roles.controller"));
app.use("/api/users", require("./app/users/users.controller"));
app.use(
  "/api/newsletters",
  require("./app/newsletters/newsletters.controller")
);
app.use("/api/states", require("./app/states/states.controller"));
app.use("/api/campaigns", require("./app/campaigns/campaigns.controller"));
app.use("/api/categories", require("./app/categories/categories.controller"));
app.use("/api/skills", require("./app/skills/skills.controller"));
app.use("/api/qualities", require("./app/qualities/qualities.controller"));
app.use(
  "/api/event-types",
  require("./app/event-types/event-types.controller")
);
app.use(
  "/api/impact-types",
  require("./app/impact-types/impact-types.controller")
);
app.use("/api/slides", require("./app/slides/slides.controller"));
app.use("/api/grades", require("./app/grades/grades.controller"));
app.use("/api/auditlogs", require("./app/auditlogs/auditlogs.controller"));
app.use(
  "/api/campaign-category-locations",
  require("./app/campaign-category-locations/campaign-category-locations.controller")
);
app.use(
  "/api/campaign-rsvps",
  require("./app/campaign_rsvps/campaign_rsvps.controller")
);
app.use(
  "/api/rsvps-informations",
  require("./app/rsvp-informations/rsvp-informations.controller")
);
app.use(
  "/api/portal-users",
  require("./app/portalusers/portalusers.controller")
);
app.use(
  "/api/ngo-category",
  require("./app/ngo-categories/ngo_categories.controller")
);
app.use(
  "/api/documents-master",
  require("./app/document-master/document-masters.controller")
);
app.use(
  "/api/user-skills",
  require("./app/user-skills/user-skills.controller")
);
app.use("/api/ngos", require("./app/ngos/ngos.controller"));
app.use(
  "/api/settings-master",
  require("./app/ngo-setting-master/ngo-settings-master.controller")
);
app.use(
  "/api/ngo-settings",
  require("./app/ngo-settings/ngo-settings.controller")
);
app.use(
  "/api/sub-categories",
  require("./app/sub-categories/sub-categories.controller")
);
app.use(
  "/api/ngo-notifications",
  require("./app/notification-ngos/notification-ngos.controller")
);
app.use("/api/data", require("./app/data_insert/datas.controller"));
app.use(
  "/api/communication-email",
  require("./app/communication-email/communication-emails.controller")
);
app.use(
  "/api/document-users",
  require("./app/document-users/document-users.controller")
);
app.use(
  "/api/transactions",
  require("./app/transactions/transactions.controller")
);
app.use("/api/products", require("./app/products/products.controller"));
app.use("/api/themes", require("./app/themes/themes.controller"));
app.use("/api/items", require("./app/items/items.controller"));
app.use("/api/orders", require("./app/orders/orders.controller"));
app.use(
  "/api/order-campaigns",
  require("./app/order-campaigns/order-campaigns.controller")
);
app.use("/api/order-ngos", require("./app/order-ngos/order-ngos.controller"));
app.use(
  "/api/banner-images",
  require("./app/themes-banner-images/themes-banner-images.controller")
);
app.use(
  "/api/impact-areas",
  require("./app/theme-impactareas/theme-impactareas.controller")
);
app.use(
  "/api/campaign-milestones",
  require("./app/campaign-milestones/campaign-milestones.controller")
);
app.use(
  "/api/kind-donations",
  require("./app/campaign-kind-donation/campaign-kind-donations.controller")
);
app.use("/api/ngo-types", require("./app/ngo-types/ngo-types.controller"));
app.use("/api/questions", require("./app/questions/questions.controller"));
app.use(
  "/api/kyc-informations",
  require("./app/kyc-informations/kyc-informations.controller")
);
app.use("/api/journals", require("./app/journals/journals.controller"));
app.use("/api/ngo-images", require("./app/ngo-images/ngo-images.controller"));
app.use(
  "/api/product-images",
  require("./app/product-images/products-images.controller")
);
app.use(
  "/api/category-images",
  require("./app/category-images/category-images.controller")
);
app.use("/api/faqs", require("./app/faqs/faqs.controller"));
app.use("/api/email", require("./app/send-email/send-emails.controller"));
app.use("/api/reviews", require("./app/reviews/reviews.controller"));
app.use("/api/cart-items", require("./app/cart-items/cart-items.controller"));
app.use("/api/carts", require("./app/carts/carts.controller"));
app.use("/api/temp-users", require("./app/temp-users/temp-users.controller"));
app.use(
  "/api/user-tokens",
  require("./app/user-tokens/user-tokens.controller")
);
app.use(
  "/api/order-donations",
  require("./app/order-donations/order-donations.controller")
);
app.use(
  "/api/bank-details",
  require("./app/bank-details/bank-details.controller")
);
app.use(
  "/api/profile-queries",
  require("./app/ngo-profile-queries/ngo-profile-queries.controller")
);
app.use(
  "/api/collections",
  require("./app/collections/collections.controller")
);
app.use(
  "/api/campaign-images",
  require("./app/campaigns-images/campaigns-images.controller")
);
app.use(
  "/api/user-notifications",
  require("./app/user-notification/user-notifications.controller")
);
app.use("/api/buckets", require("./app/buckets/buckets.controller"));
app.use(
  "/api/bucket-items",
  require("./app/bucket-items/bucket-items.controller")
);
app.use(
  "/api/payments",
  require("./app/split-payments/split-payment.controller")
);
app.use(
  "/api/google-photos",
  require("./app/google-photos/google-photos.controller")
);

// Serve the uploads folder to access files by URL
app.use("/uploads", express.static(path.join(__dirname, "uploads")));

app.use("/api/fetchImages", express.static(DIR));
app.use("/api/fetchNGODocumentsPDF", express.static(PDFDIR_DOCUMENTS));
app.use("/api/fetchThemeImage", express.static(THEME_IMAGES));
app.use("/api/fetchBannerImage", express.static(BANNER_IMAGES));

app.use(
  "/api/fetchCampaignMileStoneImage",
  express.static(CAMPAIGN_MILESTONES_IMAGES)
);
app.use("/api/fetchNgoImages", express.static(NGO_IMAGES));
app.use("/api/fetchCampaignImage", express.static(CAMPAIGN_IMAGES));
app.use("/api/fetchCampaignProfileImage", express.static(PDFDIR_DOCUMENTS));

//multiple images
app.use("/api/fetchMultipleNgoImages", express.static(NGO_MULTIPLE_IMAGES));
app.use(
  "/api/fetchMultipleCampaignImages",
  express.static(CAMPAIGN_MULTIPLE_IMAGES)
);
app.use("/api/fetchProductImages", express.static(PRODUCT_MULTIPLE_IMAGES));
app.use("/api/fetchCategoryImages", express.static(CATEGORY_IMAGES));
app.use("/api/fetchCollectionImages", express.static(COLLECTION_IMAGES));
app.use("/api/fetchSlideImages", express.static(SLIDE_IMAGES));
app.use("/api/fetchReviewImages", express.static(REVIEW_IMAGES));

//razor pay api
// app.post("/api/create-order", async (req, res) => {
//   try {
//     const { amount } = req.body;
//     const order = await razorpay.orders.create({
//       amount: amount,
//       currency: "INR",
//       receipt: `receipt_${Date.now()}`,
//     });
//     res.json({ order });
//   } catch (error) {
//     console.error(error);
//     res.status(500).json({ error: "Failed to create order" });
//   }
// });

// app.post("/api/verify-payment", async (req, res) => {
//   try {
//     const { razorpay_order_id, razorpay_payment_id, razorpay_signature } =
//       req.body;

//     const hmac = crypto.createHmac("sha256", process.env.RAZORPAY_KEY_SECRET);
//     hmac.update(`${razorpay_order_id}|${razorpay_payment_id}`);
//     const generatedSignature = hmac.digest("hex");

//     if (generatedSignature === razorpay_signature) {
//       // Save payment details in your database
//       res.json({
//         success: true,
//         data: { razorpay_order_id, razorpay_payment_id },
//       });
//     } else {
//       res.json({ success: false, error: "Invalid signature" });
//     }
//   } catch (error) {
//     console.error(error);
//     res.status(500).json({ error: "Failed to verify payment" });
//   }
// });

// global error handler
app.use(errorHandler);

// start server
const port =
  process.env.NODE_ENV === "production" ? process.env.PORT || 80 : 4000;
app.listen(port, () => console.log("Server listening on port " + port));
