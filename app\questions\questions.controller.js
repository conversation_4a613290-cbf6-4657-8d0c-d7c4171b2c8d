﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const questionsService = require("./question.service");
const { logAction } = require("../_helpers/logger");

// routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  questionsService
    .create(req.body)
    .then(() => {
      logAction(5, `Added a Question to in ${req.query.pageName} `);
      return res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  questionsService
    .getAll(req.query, req?.headers?.isadmin)
    .then((records) => {
      logAction(
        5,
        `Fetched all questions in ${req.query.pageName}`,
        req.query.pageName
      );
      return res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  questionsService
    .getById(req.params.id)
    .then((user) => res.json(user))
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    description: Joi.string().required(),
    ngo_type: Joi.string().required(),
    status: Joi.string().required(),
    mandatory: Joi.string().required(),
    createdBy: Joi.number().required(),
    updatedBy: Joi.number().allow(null, ""),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  questionsService
    .update(req.params.id, req.body)
    .then((user) => res.json(user))
    .catch(next);
}

function _delete(req, res, next) {
  questionsService
    .delete(req.params.id)
    .then(() =>
      res.json({ status: true, message: "Record deleted successfully" })
    )
    .catch(next);
}
