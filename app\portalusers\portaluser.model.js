// import  DataTypes from "sequelize";
const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    fullname: { type: DataTypes.STRING, allowNull: true },
    email: { type: DataTypes.STRING, allowNull: true },
    password: { type: DataTypes.STRING, allowNull: true },
    role_id: { type: DataTypes.INTEGER, allowNull: true },
    ngo_id: { type: DataTypes.INTEGER, allowNull: true },
    createdBy: { type: DataTypes.INTEGER, allowNull: true },
    pan: { type: DataTypes.STRING, allowNull: true },
    panNgoName: { type: DataTypes.STRING, allowNull: true },
    status: { type: DataTypes.STRING, allowNull: true },
    current_address: { type: DataTypes.STRING, allowNull: true },
    point_of_contact_mobile_number: { type: DataTypes.STRING, allowNull: true },
    token: { type: DataTypes.STRING, allowNull: true },
  };

  const options = {
    defaultScope: {
      // exclude hash by default
      attributes: { exclude: ["password"] },
    },
    scopes: {
      // include hash with this scope
      withHash: { attributes: {} },
    },
  };

  return sequelize.define("portal_users", attributes, options);
}
