﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const skillsService = require("./skill.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  skillsService
    .create(req.body)
    .then((skill) => {
      logRequest(req, `Created a new skill ${skill?.name}`, "CREATE");
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  skillsService
    .getAll(req.query, req?.headers?.isadmin)
    .then((records) => {
      logRequest(req, "Fetched all skills", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  skillsService
    .getById(req.params.id)
    .then((record) => {
      logRequest(req, `Fetched skill ${record?.name}`, "READ");
      res.json(record);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    status: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  skillsService
    .update(req.params.id, req.body)
    .then((record) => {
      logRequest(req, `Updated skill ${record?.name}`, "UPDATE");
      res.json(record);
    })
    .catch(next);
}

function _delete(req, res, next) {
  skillsService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted skill with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
