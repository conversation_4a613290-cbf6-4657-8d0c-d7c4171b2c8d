﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const documentService = require("./document-master.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);
router.delete("/deleteAll/:id", deleteAll);

module.exports = router;

function create(req, res, next) {
  documentService
    .create(req.body)
    .then((document) => {
      logRequest(
        req,
        `Added a Document: ${document?.name}, Description: ${document?.description}, Type: ${document?.type}, Document Type: ${document?.document_type}`,
        "CREATE"
      );
      return res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  documentService
    .getAll(req.query, req?.headers?.isadmin)
    .then((records) => {
      logRequest(req, "Fetched all documents", "READ");
      return res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  documentService
    .getById(req.params.id)
    .then((document) => {
      logRequest(
        req,
        `Fetched Document: ${document?.name}, Description: ${document?.description}, Type: ${document?.type}, Document Type: ${document?.document_type}`,
        "READ"
      );
      res.json(document);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    description: Joi.string().required(),
    size: Joi.string().required(),
    type: Joi.string().required(),
    status: Joi.string().required(),
    document_type: Joi.string().required(),
    mandatory: Joi.string().required(),
    isDeleted: Joi.string().optional().allow("", null),
    filetype: Joi.string().required(),
    createdBy: Joi.number().required(),
    updatedBy: Joi.number().allow(null, ""),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  documentService
    .update(req.params.id, req.body)
    .then((document) => {
      logRequest(
        req,
        `Updated Document: ${document?.name}, Description: ${document?.description}, Type: ${document?.type}, Document Type: ${document?.document_type}`,
        "UPDATE"
      );
      res.json(document);
    })
    .catch(next);
}

function _delete(req, res, next) {
  documentService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted document with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}

function deleteAll(req, res, next) {
  documentService
    .deleteAll(req.params.id)
    .then(() => {
      logRequest(
        req,
        `Deleted all documents with ID: ${req.params.id}`,
        "DELETE"
      );
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
