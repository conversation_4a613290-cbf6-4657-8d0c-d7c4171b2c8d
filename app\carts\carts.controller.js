﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const cartService = require("./cart.service");
const { logAction } = require("../_helpers/logger");

// route
router.get("/", getAll);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  cartService
    .create(req.body)
    .then((cartData) =>
      res.json({
        status: true,
        message: "Cart fetched/created successfully",
        data: cartData,
      })
    )
    .catch(next);
}

function getAll(req, res, next) {
  const { pageName } = req.query;
  cartService
    .getAll(req.query)
    .then((records) => {
      logAction(5, `Fetched All collections in ${pageName}`, pageName);
      return res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  cartService
    .getById(req.params.id)
    .then((user) => res.json(user))
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    status: Joi.string().optional().allow(null, ""),
    total_amount: Joi.number().optional().allow(null),
    user_id: Joi.number().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  cartService;
  cartService
    .update(req.params.id, req.body)
    .then((user) => res.json(user))
    .catch(next);
}

function _delete(req, res, next) {
  cartService
    .delete(req.params.id)
    .then(() =>
      res.json({ status: true, message: "Record deleted successfully" })
    )
    .catch(next);
}
