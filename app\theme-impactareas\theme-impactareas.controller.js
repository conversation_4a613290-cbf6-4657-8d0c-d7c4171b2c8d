const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const themeImpactAreaService = require("./theme-impactarea.service");

// routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);
router.delete("/user/:user_id", deleteByUserId);

module.exports = router;

function create(req, res, next) {
  themeImpactAreaService
    .create(req.body)
    .then(() =>
      res.json({ status: true, message: "Record created successfully" })
    )
    .catch(next);
}

function getAll(req, res, next) {
  themeImpactAreaService
    .getAll(req.query)
    .then((records) => {
      // return Promise.all(records.map(async element => {
      //     element.agentList = await db.Broker.count({ where: { themeId: 3, brokerId: element.id } });
      //     return element;
      // })).then(() => {
      //     res.json(records);
      //     next();
      // });

      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  themeImpactAreaService
    .getById(req.params.id)
    .then((theme) => res.json(theme))
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    description: Joi.string().required(),
    ngo_id: Joi.number().integer().allow("", null),
    position: Joi.number().integer().required(),
    count: Joi.number().integer().required(),
    updatedBy: Joi.number().integer().optional(),
    createdBy: Joi.number().integer().required(),
    status: Joi.string().required(),
  });

  validateRequest(req, next, schema);
}

function update(req, res, next) {
  themeImpactAreaService
    .update(req.params.id, req.body)
    .then((theme) => res.json(theme))
    .catch(next);
}

function _delete(req, res, next) {
  themeImpactAreaService
    .delete(req.params.id)
    .then(() =>
      res.json({ status: true, message: "Record deleted successfully" })
    )
    .catch(next);
}
function deleteByUserId(req, res, next) {
  themeImpactAreaService
    .deleteByUserId(req.params.user_id)
    .then(() => {
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
