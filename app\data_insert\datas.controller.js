﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const dataService = require("./data.service");

// routes
router.get("/", getAll);
router.post("/", create);
router.get("/:id", getById);
router.get("/state/:stateCode", getByStateCode);
router.put("/:id", update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  dataService
    .create(req.body)
    .then(() =>
      res.json({ status: true, message: "Record created successfully" })
    )
    .catch(next);
}

function getAll(req, res, next) {
  dataService
    .getAll(req.query)
    .then((records) => {
      // return Promise.all(records.map(async element => {
      //     element.agentList = await db.Broker.count({ where: { dataId: 3, brokerId: element.id } });
      //     return element;
      // })).then(() => {
      //     res.json(records);
      //     next();
      // });

      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  dataService
    .getById(req.params.id)
    .then((user) => res.json(user))
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    type: Joi.string().required(),
    status: Joi.string().required(),
    permissions: Joi.object().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  dataService
    .update(req.params.id, req.body)
    .then((user) => res.json(user))
    .catch(next);
}

function _delete(req, res, next) {
  dataService
    .delete(req.params.id)
    .then(() =>
      res.json({ status: true, message: "Record deleted successfully" })
    )
    .catch(next);
}
function getByStateCode(req, res, next) {
  dataService
    .getByStateCode(req.params.stateCode)
    .then((data) => res.json(data))
    .catch(next);
}
